import click
import os
import django
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "linarc.settings")
django.setup()

from django.db import connection, transaction
from projects.models import Site_Team, Project_Company_Shifts


class DataMigration:
    def __init__(self):
        pass

    def move_future_shift_data(self):
        """
        Move data from future_shift_code to future_shift_id column
        """
        updated_count = 0
        error_count = 0

        with transaction.atomic():
            site_teams = Site_Team.objects.all()

            with click.progressbar(
                site_teams, label="Moving column data...", width=100
            ) as bar:
                for team in bar:
                    try:
                        if team.future_shift_code:
                            # Find the shift that matches the future shift code
                            shift = Project_Company_Shifts.objects.filter(
                                pcm_id=team.pcm_id,
                                pcs_code=team.future_shift_code,  # Adjust field name as needed
                            ).first()

                            if shift:
                                team.future_shift_id = shift
                                team.save(update_fields=["future_shift_id"])
                                updated_count += 1
                            else:
                                click.echo(
                                    f"No shift found for code: {team.future_shift_code}"
                                )
                                error_count += 1
                        else:
                            # Set to None if no code exists
                            team.future_shift_id = None
                            team.save(update_fields=["future_shift_id"])

                    except Exception as e:
                        click.secho(
                            f"Error processing Site_Team {team.sit_id}: {e}", fg="red"
                        )
                        error_count += 1

        return updated_count, error_count


@click.command(name="move_data")
def move_column_data():
    """
    Move data from future_shift_code to future_shift_id column
    """
    click.echo("Starting data migration...")
    migration = DataMigration()

    try:
        updated_count, error_count = migration.move_future_shift_data()

        click.secho(f"Migration completed:", fg="green")
        click.echo(f"  Updated records: {updated_count}")
        click.echo(f"  Errors: {error_count}")

    except Exception as e:
        click.secho(f"An error occurred during migration: {e}", fg="red")


@click.command(name="validate_migration")
def validate_migration():
    """
    Validate that the data migration was successful
    """
    click.echo("Validating migration...")

    total_records = Site_Team.objects.count()
    migrated_records = Site_Team.objects.filter(future_shift_id__isnull=False).count()
    records_with_code = Site_Team.objects.filter(
        future_shift_code__isnull=False
    ).count()

    click.echo(f"Total Site_Team records: {total_records}")
    click.echo(f"Records with future_shift_id: {migrated_records}")
    click.echo(f"Records with future_shift_code: {records_with_code}")

    if migrated_records > 0:
        click.secho("✅ Migration appears successful", fg="green")
    else:
        click.secho("⚠️ No migrated records found", fg="yellow")


@click.group()
def cli():
    """Data migration utilities"""
    pass


cli.add_command(move_column_data)
cli.add_command(validate_migration)

if __name__ == "__main__":
    cli()
