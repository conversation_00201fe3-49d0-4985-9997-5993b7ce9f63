# Generated by Django 3.2.6 on 2025-05-29 05:37

from django.db import migrations, models
import django.db.models.deletion
import shortuuid.django_fields


class Migration(migrations.Migration):

    dependencies = [
        ("projects", "0001_initial"),
        ("companies", "0001_initial"),
        ("schedule", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="ScheduleProjectStatusLine",
            fields=[
                (
                    "psd_id",
                    shortuuid.django_fields.ShortUUIDField(
                        alphabet=None,
                        length=16,
                        max_length=40,
                        prefix="",
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("psd_date", models.DateField()),
                (
                    "psd_status",
                    models.CharField(
                        choices=[
                            ("REQ", "Requested"),
                            ("VAL", "validated"),
                            ("DEL", "Deleted"),
                        ],
                        default="REQ",
                        max_length=3,
                    ),
                ),
                (
                    "psd_prj_progress",
                    models.DecimalField(
                        blank=True, decimal_places=1, default=0, max_digits=12
                    ),
                ),
                ("psd_prj_duration", models.IntegerField(blank=True, null=True)),
                (
                    "psd_prj_days_todate",
                    models.PositiveIntegerField(blank=True, null=True),
                ),
                ("psd_prj_budget", models.IntegerField(blank=True, default=0)),
                ("psd_prj_ev", models.IntegerField(blank=True, default=0)),
                ("psd_prj_subs", models.JSONField(blank=True, null=True)),
                ("psd_prj_task_count", models.JSONField(blank=True, null=True)),
                ("psd_sch_data", models.JSONField(blank=True, null=True)),
                ("psd_style", models.JSONField(blank=True, null=True)),
                ("psd_created_on", models.DateTimeField(auto_now_add=True)),
                ("psd_updated_on", models.DateTimeField(auto_now=True)),
                (
                    "pcm_id",
                    models.ForeignKey(
                        db_column="pcm_id",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="projects.project_company",
                    ),
                ),
                (
                    "prj_id",
                    models.ForeignKey(
                        db_column="prj_id",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="projects.project",
                    ),
                ),
                (
                    "psd_created_by",
                    models.ForeignKey(
                        db_column="psd_created_by",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="psd_created_by",
                        to="companies.company_employee",
                    ),
                ),
            ],
            options={
                "db_table": "schedule_project_status_line",
            },
        ),
    ]
