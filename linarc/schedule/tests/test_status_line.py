from rest_framework.test import APITestCase, APIClient
from datetime import date, timedelta
from companies.models import login_user as LoginUser
from schedule.tests.data_factory import (
    create_company,
    create_company_role,
    create_user,
    create_employee,
    create_company_employee_roles,
    create_project,
    create_project_company,
    add_rbac,
    create_tasks,
    create_working_days,
)


class ScheduleDraftTestCases(APITestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.company = create_company()
        cls.roles = create_company_role(cls.company)
        pm_role = cls.roles[0]
        cls.login_user = create_user("testuser", "testpass")
        cls.employee = create_employee(cls.login_user, cls.company, pm_role)
        cls.employee_role = create_company_employee_roles(
            cls.employee, cls.company, pm_role
        )
        cls.project = create_project(cls.employee)
        cls.project_company = create_project_company(
            cls.project, cls.employee, cls.company
        )
        create_working_days(cls.project_company, cls.project)
        create_tasks(cls.project_company, cls.employee)
        add_rbac()

        api = APIClient()
        response = api.post(
            "/api-token-auth/",
            {"username": "testuser", "password": "testpass"},
            format="json",
        )
        token = response.data.get("access")
        api.credentials(HTTP_AUTHORIZATION=f"Bearer {token}")

        cls.status_line_date = (date.today() + timedelta(days=1)).strftime("%Y-%m-%d")
        create_payload = {
            "statusLineDate": cls.status_line_date,
            "statusLineStyle": {"stroke": "#00C320", "stroke-width": 6},
        }
        response = api.post(
            f"/api/v1/schedule/status-line/{cls.project.prj_id}/{cls.project_company.pcm_id}/",
            data=create_payload,
            format="json",
        )
        cls.created_id = response.data.get("result", {}).get("psd_id")

    def setUp(self):
        self.client = APIClient()
        response = self.client.post(
            "/api-token-auth/",
            {"username": "testuser", "password": "testpass"},
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        self.token = response.data.get("access")
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {self.token}")

    def test_create_status_line(self):
        create_payload = {
            "statusLineDate": (date.today() + timedelta(days=2)).strftime("%Y-%m-%d"),
            "statusLineStyle": {"stroke": "#00C320", "stroke-width": 6},
        }
        response = self.client.post(
            f"/api/v1/schedule/status-line/{self.project.prj_id}/{self.project_company.pcm_id}/",
            data=create_payload,
            format="json",
        )

        self.assertEqual(response.status_code, 201)

    def test_get_all_status_line(self):
        response = self.client.get(
            f"/api/v1/schedule/status-line/{self.project.prj_id}/{self.project_company.pcm_id}/"
        )
        self.assertEqual(response.status_code, 200)

    def test_get_status_line_with_schedule(self):
        response = self.client.get(
            f"/api/v1/schedule/status-line/get-status-line-with-schedule/{self.project_company.pcm_id}/"
        )
        self.assertEqual(response.status_code, 200)

    def test_get_status_line_by_ids(self):
        response = self.client.get(
            f"/api/v1/schedule/status-line/status-meta-data/{self.project_company.pcm_id}/",
            data={"psdIds": self.created_id},
        )
        self.assertEqual(response.status_code, 200)

    def test_delete_status_line(self):
        response = self.client.delete(
            f"/api/v1/schedule/status-line/status-date/{self.created_id}/",
        )
        self.assertEqual(response.status_code, 200)
