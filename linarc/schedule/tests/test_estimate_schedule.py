from django.test import TestCase
from unittest.mock import patch
from rest_framework.test import APIClient
from schedule.models import ScheduleItemEstimateAllocate
from rest_framework.authtoken.models import Token
from companies.models import login_user as LoginUser
from schedule.tests.data_factory import (
    make_company,
    make_company_employee,
    make_sch_recipe,
    make_project,
    make_project_company,
    make_schedule_estimate,
)


class LinkEstimatePatchApiViewsetTestCase(TestCase):
    @classmethod
    def setUpClass(cls):
        super(LinkEstimatePatchApiViewsetTestCase, cls).setUpClass()

        cls.user = LoginUser.objects.create_user(username="test", password="test")
        companies = make_company(3)
        cls.gc_company = companies[0]
        cls.sc_company = companies[1]
        cls.sc_company2 = companies[2]
        cls.project = make_project(1)[0]
        cls.gc_pcm = make_project_company(
            cls.project, cls.gc_company, pcm_cbt_code="GC", qty=1
        )[0]
        cls.sc_pcm = make_project_company(
            cls.project, cls.sc_company, pcm_cbt_code="SC", qty=1
        )[0]
        cls.sc_pcm2 = make_project_company(
            cls.project, cls.sc_company2, pcm_cbt_code="SC", qty=1
        )[0]
        cls.company_employee = make_company_employee(1)[0]

        # Fix the calls to make_sch_recipe by reviewing its signature
        # make_sch_recipe(pcm_id, prj_id, qty=1, user_id="lordv2064", sch_name="item")
        cls.schedule_recipe1 = make_sch_recipe(
            cls.gc_pcm.pcm_id,
            cls.project.prj_id,
            qty=3,
            user_id=cls.company_employee.user_id,
            sch_name="item1",
        )[0]
        cls.schedule_recipe2 = make_sch_recipe(
            cls.gc_pcm.pcm_id,
            cls.project.prj_id,
            qty=3,
            user_id=cls.company_employee.user_id,
            sch_name="item2",
        )[0]
        cls.schedule_recipe3 = make_sch_recipe(
            cls.gc_pcm.pcm_id,
            cls.project.prj_id,
            qty=3,
            user_id=cls.company_employee.user_id,
            sch_name="item3",
        )[0]
        cls.schedule_recipe4 = make_sch_recipe(
            cls.gc_pcm.pcm_id,
            cls.project.prj_id,
            qty=3,
            user_id=cls.company_employee.user_id,
            sch_name="item4",
        )[0]
        cls.schedule_recipe5 = make_sch_recipe(
            cls.gc_pcm.pcm_id,
            cls.project.prj_id,
            qty=3,
            user_id=cls.company_employee.user_id,
            sch_name="item5",
        )[0]

    def setUp(self):
        self.client = APIClient()
        token, _ = Token.objects.get_or_create(user=self.user)
        self.client.credentials(HTTP_AUTHORIZATION="Token " + token.key)

    @patch("schedule.models.ScheduleItem._update_days")
    def test_patch_link_estimate_allocates_costs_by_hours_ratio(self, mock_update_days):
        self.estimate = make_schedule_estimate(self.sc_pcm, 1)[0]

        data = [
            {
                "sch_id": self.schedule_recipe1.sch_id,
                "ces_id": self.estimate.ces_id,
                "sch_resp_pcm_id": self.sc_pcm.pcm_id,
                "estimate_data": {"sie_effort_hrs": 5},
            }
        ]

        url = "/api/v1/schedule/link-estimate/update/"
        response = self.client.patch(url, data=data, format="json")
        # Check if allocation was created
        allocation = ScheduleItemEstimateAllocate.objects.filter(
            sch_id=self.schedule_recipe1.sch_id, ces_id=self.estimate.ces_id
        ).first()
        self.assertIsNotNone(allocation)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(allocation.sie_allocation_type, "HR")  # Hours ratio allocation
        self.assertEqual(allocation.sie_effort_hrs, 5)

    @patch("schedule.models.ScheduleItem._update_days")
    def test_link_estimate_quantity_ratio(self, mock_update_days):
        """Test linking an estimate with quantity ratio allocation"""
        self.estimate = make_schedule_estimate(self.sc_pcm, 1)[0]

        data = [
            {
                "sch_id": self.schedule_recipe1.sch_id,
                "ces_id": self.estimate.ces_id,
                "sch_resp_pcm_id": self.sc_pcm.pcm_id,
                "estimate_data": {"sie_qty": 2},
            }
        ]

        url = "/api/v1/schedule/link-estimate/update/"
        response = self.client.patch(url, data=data, format="json")

        # Check if allocation was created
        allocation = ScheduleItemEstimateAllocate.objects.filter(
            sch_id=self.schedule_recipe1.sch_id, ces_id=self.estimate.ces_id
        ).first()

        self.assertIsNotNone(allocation)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(
            allocation.sie_allocation_type, "QT"
        )  # Quantity ratio allocation
        self.assertEqual(allocation.sie_qty, 2)

    @patch("schedule.models.ScheduleItem._update_days")
    def test_link_estimate_percentage_allocation(self, mock_update_days):
        """Test linking an estimate with percentage allocation"""
        self.estimate = make_schedule_estimate(self.sc_pcm, 1)[0]

        data = [
            {
                "sch_id": self.schedule_recipe1.sch_id,
                "ces_id": self.estimate.ces_id,
                "sch_resp_pcm_id": self.sc_pcm.pcm_id,
                "estimate_data": {"allocation_percentage": 50},
            }
        ]

        url = "/api/v1/schedule/link-estimate/update/"
        response = self.client.patch(url, data=data, format="json")

        # Check if allocation was created
        allocation = ScheduleItemEstimateAllocate.objects.filter(
            sch_id=self.schedule_recipe1.sch_id, ces_id=self.estimate.ces_id
        ).first()

        self.assertIsNotNone(allocation)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(allocation.sie_allocation_type, "PR")  # Percentage allocation

    @patch("schedule.models.ScheduleItem._update_days")
    def test_link_estimate_amount_allocation(self, mock_update_days):
        """Test linking an estimate with amount allocation"""
        self.estimate = make_schedule_estimate(self.sc_pcm, 1)[0]

        data = [
            {
                "sch_id": self.schedule_recipe1.sch_id,
                "ces_id": self.estimate.ces_id,
                "sch_resp_pcm_id": self.sc_pcm.pcm_id,
                "estimate_data": {"sie_allocated_amount": 1250},  # Example amount
            }
        ]

        url = "/api/v1/schedule/link-estimate/update/"
        response = self.client.patch(url, data=data, format="json")

        # Check if allocation was created
        allocation = ScheduleItemEstimateAllocate.objects.filter(
            sch_id=self.schedule_recipe1.sch_id, ces_id=self.estimate.ces_id
        ).first()

        self.assertIsNotNone(allocation)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(
            allocation.sie_allocation_type, "TA"
        )  # Total amount allocation
        self.assertEqual(allocation.sie_allocated_amount, 1250)

    @patch("schedule.models.ScheduleItem._update_days")
    def test_link_estimate_schedule_not_found(self, mock_update_days):
        """Test error handling when schedule doesn't exist"""
        self.estimate = make_schedule_estimate(self.sc_pcm, 1)[0]

        data = [
            {
                "sch_id": "nonexistent-schedule",
                "ces_id": self.estimate.ces_id,
                "sch_resp_pcm_id": self.sc_pcm.pcm_id,
                "estimate_data": {"sie_effort_hrs": 5},
            }
        ]

        url = "/api/v1/schedule/link-estimate/update/"
        response = self.client.patch(url, data=data, format="json")

        self.assertEqual(response.status_code, 400)
        self.assertIn("Schedule item doesn't exist", response.data.get("message", ""))

    @patch("schedule.models.ScheduleItem._update_days")
    def test_link_estimate_estimate_not_found(self, mock_update_days):
        """Test error handling when estimate doesn't exist"""
        data = [
            {
                "sch_id": self.schedule_recipe1.sch_id,
                "ces_id": "nonexistent-estimate",
                "sch_resp_pcm_id": self.sc_pcm.pcm_id,
                "estimate_data": {"sie_effort_hrs": 5},
            }
        ]

        url = "/api/v1/schedule/link-estimate/update/"
        response = self.client.patch(url, data=data, format="json")

        self.assertEqual(response.status_code, 400)
        self.assertIn("Estimate doesn't exist", response.data.get("message", ""))

    @patch("schedule.models.ScheduleItem._update_days")
    def test_link_estimate_already_exists(self, mock_update_days):
        """Test error handling when an allocation already exists"""
        self.estimate = make_schedule_estimate(self.sc_pcm, 1)[0]

        # First create an allocation
        data = [
            {
                "sch_id": self.schedule_recipe1.sch_id,
                "ces_id": self.estimate.ces_id,
                "sch_resp_pcm_id": self.sc_pcm.pcm_id,
                "estimate_data": {"sie_effort_hrs": 5},
            }
        ]

        url = "/api/v1/schedule/link-estimate/update/"
        response = self.client.patch(url, data=data, format="json")
        self.assertEqual(response.status_code, 200)

        # Try to create it again
        response = self.client.patch(url, data=data, format="json")

        # Should get a 400 error
        self.assertEqual(response.status_code, 400)
        self.assertIn("already exists", response.data.get("message", ""))

    @patch("schedule.models.ScheduleItem._update_days")
    def test_link_estimate_exceeds_available(self, mock_update_days):
        """Test error handling when allocation exceeds available resources"""
        self.estimate = make_schedule_estimate(self.sc_pcm, 1)[0]

        # Try to allocate more hours than available in the estimate
        data = [
            {
                "sch_id": self.schedule_recipe1.sch_id,
                "ces_id": self.estimate.ces_id,
                "sch_resp_pcm_id": self.sc_pcm.pcm_id,
                "estimate_data": {
                    "sie_effort_hrs": 1000
                },  # Large number to exceed available hours
            }
        ]

        url = "/api/v1/schedule/link-estimate/update/"

        response = self.client.patch(url, data=data, format="json")
        self.assertEqual(response.status_code, 400)
        self.assertIn("exceed available", response.data.get("message", ""))


class DeLinkEstimateApiViewsetTestCase(TestCase):
    @classmethod
    def setUpClass(cls):
        super(DeLinkEstimateApiViewsetTestCase, cls).setUpClass()

        cls.user = LoginUser.objects.create_user(username="test", password="test")
        companies = make_company(3)
        cls.gc_company = companies[0]
        cls.sc_company = companies[1]
        cls.sc_company2 = companies[2]
        cls.project = make_project(1)[0]
        cls.gc_pcm = make_project_company(
            cls.project, cls.gc_company, pcm_cbt_code="GC", qty=1
        )[0]
        cls.sc_pcm = make_project_company(
            cls.project, cls.sc_company, pcm_cbt_code="SC", qty=1
        )[0]
        cls.company_employee = make_company_employee(1)[0]

        cls.schedule_recipe = make_sch_recipe(
            cls.gc_pcm.pcm_id,
            cls.project.prj_id,
            qty=3,
            user_id=cls.company_employee.user_id,
            sch_name="test_item",
        )[0]

    def setUp(self):
        self.client = APIClient()
        token, _ = Token.objects.get_or_create(user=self.user)
        self.client.credentials(HTTP_AUTHORIZATION="Token " + token.key)

        # Create estimate and allocation for each test
        self.estimate = make_schedule_estimate(self.sc_pcm, 1)[0]

        # Create allocation with required fields
        self.allocation = ScheduleItemEstimateAllocate.objects.create(
            sch_id=self.schedule_recipe,
            ces_id=self.estimate,
            pcm_id=self.sc_pcm,  # Use the object reference, not just the ID
            sie_effort_hrs=5,
            sie_lab_cost=100.0,
            sie_mat_cost=10.0,
            sie_eqp_cost=10.0,
            sie_oh_cost=10.0,
            sie_other_cost=10.0,
            sie_subc_cost=0.0,  # Required field
            sie_profit=0.0,  # Required field
            sie_qty=2,
            sie_allocated_amount=130.0,
            sie_allocation_type="HR",  # Hours allocation type
        )

    @patch("schedule.models.ScheduleItem._update_days")
    def test_delink_estimate_success(self, mock_update_days):
        """Test successfully delinking an estimate from a schedule item"""
        # Verify allocation exists before test
        self.assertTrue(
            ScheduleItemEstimateAllocate.objects.filter(
                sch_id=self.schedule_recipe.sch_id, ces_id=self.estimate.ces_id
            ).exists()
        )
        # URL for delink endpoint
        url = f"/api/v1/schedule/estimate/{self.estimate.ces_id}/de-link/{self.schedule_recipe.sch_id}/"
        response = self.client.patch(url, format="json")
        self.assertEqual(response.status_code, 200)

        # # Verify the allocation is removed
        self.assertFalse(
            ScheduleItemEstimateAllocate.objects.filter(
                sch_id=self.schedule_recipe.sch_id, ces_id=self.estimate.ces_id
            ).exists()
        )

    @patch("schedule.models.ScheduleItem._update_days")
    def test_delink_estimate_not_found(self, mock_update_days):
        """Test error handling when allocation doesn't exist"""
        # Delete the allocation first
        self.allocation.delete()

        # URL for delink endpoint
        url = f"/api/v1/schedule/estimate/{self.estimate.ces_id}/de-link/{self.schedule_recipe.sch_id}/"
        response = self.client.patch(url, format="json")

        self.assertEqual(response.status_code, 400)
        self.assertIn("No estimate allocation found", response.data.get("message", ""))

    @patch("schedule.models.ScheduleItem._update_days")
    def test_delink_estimate_schedule_not_found(self, mock_update_days):
        """Test error handling when schedule doesn't exist"""
        # URL for delink endpoint with non-existent schedule ID
        url = f"/api/v1/schedule/estimate/{self.estimate.ces_id}/de-link/non-existent-schedule-id/"
        response = self.client.patch(url, format="json")
        self.assertEqual(response.status_code, 400)
