# Generated by Django 3.2.6 on 2025-06-10 16:10

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("projects", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="site_team",
            name="future_shift_id",
            field=models.ForeignKey(
                blank=True,
                db_column="future_shift_id",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="site_team_future_shift",
                to="projects.project_company_shifts",
            ),
        ),
        migrations.AddField(
            model_name="site_team",
            name="scheduled_removal_date",
            field=models.DateField(
                blank=True,
                db_index=True,
                help_text="Date when the team member is scheduled to be removed from site",
                null=True,
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="site_team",
            name="future_shift_date",
            field=models.DateField(db_index=True, null=True),
        ),
        migrations.AlterField(
            model_name="site_team",
            name="pcs_id",
            field=models.ForeignKey(
                db_column="pcs_id",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="site_team_current_shift",
                to="projects.project_company_shifts",
            ),
        ),
    ]
