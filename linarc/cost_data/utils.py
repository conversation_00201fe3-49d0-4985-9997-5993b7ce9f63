from rest_framework import status
from rest_framework.response import Response
from django.utils import timezone
from datetime import date, timedelta
from django.db import connection
from .models import ProjectCostSetup
from companies.models import CompanyRole, company_employee
from django.db.models import Value
from django.db.models.functions import Concat

RECURRING_COST_ERRORS = {
    "missing_dates": "Start date and end date are required for non-Adhoc frequencies.",
    "invalid_date_order": "Start date cannot be after end date.",
    "missing_rate": "Rate is required for non-Adhoc frequencies.",
    "invalid_rate": "Rate must be a valid numeric value.",
    "non_positive_rate": "Rate is required and must be greater than zero.",
    "not_found": "Cost configuration not found.",
    "invalid_delete": "Cannot delete cost configuration with associated records.",
    "already_deactivated": "Alread Inactive.",
    "pending_deactivate": "Cannot deactivate pending cost configuration.",
    "already_approved": "No update allowed for approved item.",
    "user_not_found": "User not found.",
    "invalid_operation": "Invalid operations not allowed",
    "invalid_approve": "Invalid approve operation.",
    "duplicate_config": "Already cost configuration exists for this project.",
}

UNAPPROVED_COST_ERRORS = {
    "not_found": "Unapproved cost not found.",
    "already_approved": "Cost entry already approved.",
    "invalid_status": "Invalid status transition.",
    "invalid_delete": "Cannot delete approved cost entries.",
    "bulk_invalid": "One or more cost entries could not be processed.",
    "user_not_approver": "Current user is not the approver for this cost entry.",
    "invalid_amount": "Cost amount must be a positive numeric value.",
    "unauthorized": "You do not have permission to perform this action.",
    "signature_not_found": "Signature not found for the user.",
}

SUCCESS_MESSAGES = {
    "created": "Cost configuration created successfully.",
    "updated": "Cost configuration updated successfully.",
    "deleted": "Cost configuration deleted successfully.",
    "fetched": "Cost configuration fetched successfully.",
    "health": "Recurring Cost API is operational.",
    "deactivated": "Cost Configuration deactivated successfully.",
    "approved": "Cost Configuration approved successfully.",
    "uac_created": "Unapproved cost entry created successfully.",
    "uac_updated": "Unapproved cost entry updated successfully.",
    "uac_deleted": "Unapproved cost entry deleted successfully.",
    "uac_bulk_deleted": "Unapproved cost entries marked as deleted successfully.",
    "uac_fetched": "Unapproved cost entries fetched successfully.",
    "uac_approved": "Cost entry approved successfully.",
    "uac_bulk_approved": "Cost entries approved successfully.",
    "uac_health": "Unapproved Cost API is operational.",
    "uac_submitted": "Unapproved cost entry submitted successfully.",
    "uac_revised": "Unapproved cost entry sent for revise successfully.",
}


def error_response(message, status_code=status.HTTP_400_BAD_REQUEST):
    return Response({"status": "error", "message": message}, status=status_code)


def success_response(message, data=None, status_code=status.HTTP_200_OK):
    return Response(
        {"status": "success", "message": message, "data": data}, status=status_code
    )


default_response_body = {
    "200": "OK",
    "400": "Bad Request",
    "404": "Not Found",
    "401": "Unauthorized",
    "500": "Internal Server Error",
    "403": "Forbidden",
}


def get_material_cost_data(cost_data, now):
    data = {
        "smc_date": now.date() if now else timezone.now().date(),
        "smc_amount": cost_data.get("uac_actual_cost"),
        "smc_vendor": cost_data.get("uac_vendor", ""),
        "task_code": cost_data.get("uac_cost_code"),
        "task_name": cost_data.get("uac_cost_name"),
        "pcm_id": cost_data.get("pcm_id"),
        "smc_details": cost_data.get("uac_meta_data", {}),
        "smc_createdby": cost_data.get(
            "uac_added_by"
        ),  # Need to change it can be creator or approved
        "uac_id": cost_data.get("uac_id"),
    }
    return data


def get_other_cost_data(cost_data, now):
    data = {
        "soc_date": now,
        "soc_amount": cost_data.get("uac_actual_cost"),
        "soc_vendor": cost_data.get("uac_vendor"),
        "task_code": cost_data.get("uac_cost_code"),
        "task_name": cost_data.get("uac_cost_name"),
        "pcm_id": cost_data.get("pcm_id"),
        "soc_details": cost_data.get("uac_meta_data"),
        "soc_createdby": cost_data.get(
            "uac_added_by"
        ),  # Need to change it can be creator or approved
        "soc_type": "OT",
        "uac_id": cost_data.get("uac_id"),
    }
    return data


def get_overhead_cost_data(cost_data, now):
    data = {
        "soc_date": now,
        "soc_amount": cost_data.get("uac_actual_cost"),
        "soc_vendor": cost_data.get("uac_vendor"),
        "task_code": cost_data.get("uac_cost_code"),
        "task_name": cost_data.get("uac_cost_name"),
        "pcm_id": cost_data.get("pcm_id"),
        "soc_details": cost_data.get("uac_meta_data"),
        "soc_createdby": cost_data.get(
            "uac_added_by"
        ),  # Need to change it can be creator or approved
        "soc_type": "OH",
        "uac_id": cost_data.get("uac_id"),
    }
    return data


def get_subcontractor_cost_data(cost_data, now):
    data = {
        "soc_date": timezone.now(),
        "soc_amount": cost_data.get("uac_actual_cost"),
        "soc_vendor": cost_data.get("uac_vendor"),
        "task_code": cost_data.get("uac_cost_code"),
        "task_name": cost_data.get("uac_cost_name"),
        "pcm_id": cost_data.get("pcm_id"),
        "soc_details": cost_data.get("uac_meta_data"),
        "soc_createdby": cost_data.get(
            "uac_added_by"
        ),  # Need to change it can be creator or approved
        "soc_type": "SC",
    }
    return data


def get_equipment_cost_data(cost_data, now):
    data = {
        "sec_date": now,
        "sec_amount": cost_data.get("uac_actual_cost"),
        "sec_vendor": cost_data.get("uac_vendor"),
        "task_code": cost_data.get("uac_cost_code"),
        "task_name": cost_data.get("uac_cost_name"),
        "pcm_id": cost_data.get("pcm_id"),
        "sec_details": cost_data.get("uac_meta_data"),
        "sec_createdby": cost_data.get(
            "uac_added_by"
        ),  # Need to change it can be creator or approved
    }
    return data


def get_labour_cost_data(cost_data, now):
    data = {
        "slc_date": now,
        "slc_amount": cost_data.get("uac_actual_cost"),
        "slc_vendor": cost_data.get("uac_vendor"),
        "task_code": cost_data.get("uac_cost_code"),
        "task_name": cost_data.get("uac_cost_name"),
        "pcm_id": cost_data.get("pcm_id"),
        "slc_details": cost_data.get("uac_meta_data"),
        "slc_createdby": cost_data.get(
            "uac_added_by"
        ),  # Need to change it can be creator or approved
    }
    return data


def get_payload(type, cost_data, now):
    payload_func_map = {
        "MAT": get_material_cost_data,
        "OT": get_other_cost_data,
        "OH": get_overhead_cost_data,
        "SUB": get_subcontractor_cost_data,
        "EQU": get_equipment_cost_data,
        "LAB": get_labour_cost_data,
    }
    payload_func = payload_func_map.get(type)
    if payload_func:
        return payload_func(cost_data, now)
    else:
        raise ValueError(f"Invalid type: {type}")


def dictfetchall(cursor):
    "Return all rows from a cursor as a dict"
    columns = [col[0] for col in cursor.description]
    return [dict(zip(columns, row)) for row in cursor.fetchall()]


def get_user_having_permission(pcm_id, syo_code, permissions):
    query = """
        SELECT DISTINCT
            ce.user_id,
            ce.emp_first_name,
            ce.emp_last_name,
            crp.crp_permission,
            syo.syo_code
        FROM
            project_team pt
        JOIN
            company_employee ce ON ce.user_id = pt.user_id
        JOIN
            rbac_companyrolepermission crp ON ce.rol_id = crp.rol_id
        JOIN
            rbac_system_objects syo ON crp.syo_code = syo.syo_id
        WHERE
            pt.pcm_id = %s
            AND pt.prt_status = 'AC'
            AND syo.syo_code = %s
            AND crp.crp_permission = %s
            AND ce.emp_status = 'AC';
        """
    params = [pcm_id, syo_code, permissions]
    with connection.cursor() as cursor:
        cursor.execute(query, params)
        return dictfetchall(cursor)


def should_write_data(frequency, last_generated_on):
    today = date.today()

    if today == last_generated_on:
        return False

    if frequency == ProjectCostSetup.FrequencyChoices.DAILY:
        return True

    if frequency == ProjectCostSetup.FrequencyChoices.WEEKLY:
        # Assuming Sunday is the last day of the week (weekday=6)
        return today.weekday() == 6

    if frequency == ProjectCostSetup.FrequencyChoices.MONTHLY:
        # Last day of the month
        next_day = today + timedelta(days=1)
        return next_day.month != today.month

    if frequency == ProjectCostSetup.FrequencyChoices.QUARTERLY:
        # End of March, June, September, December
        return today.month in [3, 6, 9, 12] and is_last_day_of_month(today)

    if frequency == "HALF_YEARLY":
        # End of June, December
        return today.month in [6, 12] and is_last_day_of_month(today)

    if frequency == ProjectCostSetup.FrequencyChoices.YEARLY:
        # December 31
        return today.month == 12 and today.day == 31

    return False


def is_last_day_of_month(check_date):
    next_day = check_date + timedelta(days=1)
    return next_day.month != check_date.month


def format_unapproved_cost_meta_data(meta_data):
    formatted_data = {}

    # Add receipt number if available
    if "receipt_no" in meta_data and meta_data["receipt_no"]:
        formatted_data["receipt_no"] = meta_data["receipt_no"]

    # Add cost entries if available
    if "cost_entries" in meta_data and isinstance(meta_data["cost_entries"], list):
        formatted_data["cost_entries"] = meta_data["cost_entries"]

    return formatted_data


def is_pm(user_id, pcm_id):
    query = """
        SELECT rcr.rol_type
        FROM rbac_company_role rcr
        JOIN company_employee_roles cer ON cer.rol_id = rcr.rol_id
        JOIN project_company pc ON pc.cmp_id = rcr.cmp_id
        WHERE cer.user_id = %s
          AND cer.is_primary = TRUE
          AND pc.pcm_id = %s;
    """
    params = [user_id, pcm_id]
    with connection.cursor() as cursor:
        cursor.execute(query, params)
        result = cursor.fetchone()
        if result and CompanyRole.roltype_choices.PROJECT_MANAGER in result:
            return True
    return False


activity_log_key_mapping_rc = {
    "pcs_cost_name": "Cost Name",
    "pcs_cost_code": "Cost Code",
    "pcs_cost_type": "Cost Type",
    "pcs_frequency": "Frequency",
    "pcs_start_date": "Start Date",
    "pcs_end_date": "End Date",
    "pcs_rate": "Rate",
    "pcs_approver": "Approver",
    "pcs_vendor": "Vendor",
    "pcs_cost_source": "Source",
    "pcs_cost_relates_to": "Cost Relates To",
}

activity_log_key_mapping_uac = {
    "uac_cost_name": "Cost Name",
    "uac_cost_code": "Cost Code",
    "uac_cost_relates_to": "Cost Relates To",
    "uac_frequency": "Frequency",
    "uac_approver": "Approver",
    "uac_vendor": "Vendor",
    "uac_cost_source": "Source",
    "uac_actual_cost": "Amount",
    "uac_qty": "Quantity",
}


def get_full_name(user_id):
    return (
        company_employee.objects.filter(user_id=user_id)
        .only("emp_first_name", "emp_last_name")
        .annotate(full_name=Concat("emp_first_name", Value(" "), "emp_last_name"))
        .values_list("full_name", flat=True)
        .first()
    ) or "-"


def get_pms_list(pcm_id):
    query = """
        SELECT DISTINCT
            ce.user_id,
            ce.emp_first_name,
            ce.emp_last_name,
            ce.emp_email
        FROM rbac_company_role rcr
        JOIN company_employee_roles cer ON cer.rol_id = rcr.rol_id
        JOIN project_company pc ON pc.cmp_id = rcr.cmp_id
        JOIN project_team pt ON pt.user_id = cer.user_id AND pt.pcm_id = pc.pcm_id
        JOIN company_employee ce ON ce.user_id = cer.user_id
        WHERE rcr.rol_type = 'PM'
        AND pc.pcm_id = %s
        AND pt.prt_status = 'AC';
    """
    with connection.cursor() as cursor:
        cursor.execute(query, [pcm_id])
        return dictfetchall(cursor)
