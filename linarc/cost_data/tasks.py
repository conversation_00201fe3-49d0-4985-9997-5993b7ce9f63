from celery import shared_task
from django.core.management import call_command
import logging

logger = logging.getLogger(__name__)


@shared_task
def generate_recurring_costs():
    """
    Celery task to generate recurring cost data.
    """
    # Call the management command to update Site_Team status
    try:
        logger.info("Starting generate_recurring_costs")
        call_command("generate_rc")
        logger.info("Successfully completed generate recurring_costs")
        return True
    except Exception as e:
        logger.error(f"Error in generating recurring cost: {e}")
        raise


@shared_task
def close_recurring_costs():
    """
    Celery task to close recurring costs.
    """
    try:
        logger.info("Starting close_recurring_costs task")
        call_command("close_rc")
        logger.info("Successfully completed close_recurring_costs task")
        return True
    except Exception as e:
        logger.error(f"Error in close_recurring_costs task: {e}")
        raise
