# Generated by Django 3.2.6 on 2025-06-10 12:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("cost_data", "0004_rename_uac_uac_approved_on_unapprovedcost_uac_approved_on"),
    ]

    operations = [
        migrations.AddConstraint(
            model_name="projectcostsetup",
            constraint=models.UniqueConstraint(
                condition=models.Q(
                    ("pcs_frequency", "ADHOC"),
                    models.Q(("pcs_status", "DELETED"), _negated=True),
                ),
                fields=(
                    "pcs_cost_code",
                    "pcs_frequency",
                    "pcs_cost_relates_to",
                    "pcs_cost_source",
                    "pcm_id",
                ),
                name="unique_adhoc_cost_entry_when_not_deleted",
            ),
        ),
    ]
