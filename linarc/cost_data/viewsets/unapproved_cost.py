from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from linarc.utils.swagger_utils import get_response_schema
from rest_framework import viewsets, status
from ..models import UnapprovedCost, ProjectCostSetup
from django.db.models import Q
from ..serializers import (
    UnapprovedCostSerializer,
    SmcSerializerPost,
    ScheduleEquipmentSerializer,
    SOCSerializer,
    ScheduleLabourCostSerializer,
    ScheduleSubCostSerializer as SubContractorCostSerializer,
)
from django.core.exceptions import ValidationError
from companies.models import company_employee
from django.utils import timezone
from ..utils import (
    UNAPPROVED_COST_ERRORS,
    SUCCESS_MESSAGES,
    error_response,
    success_response,
    default_response_body,
    get_payload,
    format_unapproved_cost_meta_data,
    is_pm,
    get_full_name,
    activity_log_key_mapping_uac,
    get_pms_list,
)
import traceback
from .recurring_cost import format_serializer_errors, RECURRING_COST_ERRORS
from django.db import transaction
from django.forms.models import model_to_dict
from shared.models.employee_signature import EmployeeSignature, ObjectSignature
from django.contrib.contenttypes.models import ContentType
from rbac.decorators import PermissionSetV2, check_permissionV2
from projects.services import activity_log_templates
from notification.notification_v2_services import EventService

CostRelatesToSerializer = {
    ProjectCostSetup.CostRelatesToChoices.MATERIAL: SmcSerializerPost,
    ProjectCostSetup.CostRelatesToChoices.OTHER: SOCSerializer,
    ProjectCostSetup.CostRelatesToChoices.OVERHEAD: SOCSerializer,
    ProjectCostSetup.CostRelatesToChoices.EQUIPMENT: ScheduleEquipmentSerializer,
    ProjectCostSetup.CostRelatesToChoices.LABOR: ScheduleLabourCostSerializer,
    ProjectCostSetup.CostRelatesToChoices.SUBCONTRACTOR: SubContractorCostSerializer,
}
from common_utils.logger import logger


class UnapprovedCostViewSet(viewsets.ViewSet):
    @swagger_auto_schema(
        tags=["Unapproved Cost"],
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Unapproved cost entries fetched successfully.",
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_ARRAY,
                            items=openapi.Schema(
                                type=openapi.TYPE_OBJECT,
                                properties={
                                    "uac_id": openapi.Schema(type=openapi.TYPE_STRING),
                                    "pcm_id": openapi.Schema(type=openapi.TYPE_STRING),
                                    "cost_setup_id": openapi.Schema(
                                        type=openapi.TYPE_STRING, nullable=True
                                    ),
                                    "uac_actual_cost": openapi.Schema(
                                        type=openapi.TYPE_NUMBER
                                    ),
                                    "uac_status": openapi.Schema(
                                        type=openapi.TYPE_STRING,
                                        enum=[
                                            "PENDING",
                                            "UNDER_REVIEW",
                                            "APPROVED",
                                            "RESUBMIT",
                                            "DELETED",
                                        ],
                                    ),
                                    "uac_added_on": openapi.Schema(
                                        type=openapi.TYPE_STRING,
                                        format=openapi.FORMAT_DATETIME,
                                    ),
                                    "uac_vendor": openapi.Schema(
                                        type=openapi.TYPE_STRING, nullable=True
                                    ),
                                    "uac_cost_code": openapi.Schema(
                                        type=openapi.TYPE_STRING, nullable=True
                                    ),
                                    "uac_cost_name": openapi.Schema(
                                        type=openapi.TYPE_STRING, nullable=True
                                    ),
                                    "uac_meta_data": openapi.Schema(
                                        type=openapi.TYPE_OBJECT
                                    ),
                                    "uac_qty": openapi.Schema(
                                        type=openapi.TYPE_NUMBER, nullable=True
                                    ),
                                    "added_by_details": openapi.Schema(
                                        type=openapi.TYPE_OBJECT, nullable=True
                                    ),
                                    "approved_by_details": openapi.Schema(
                                        type=openapi.TYPE_OBJECT, nullable=True
                                    ),
                                    "updated_by_details": openapi.Schema(
                                        type=openapi.TYPE_OBJECT, nullable=True
                                    ),
                                    "cost_setup_details": openapi.Schema(
                                        type=openapi.TYPE_OBJECT, nullable=True
                                    ),
                                },
                            ),
                        ),
                    },
                ),
            ),
            400: openapi.Response(description="Bad Request"),
            401: openapi.Response(description="Unauthorized"),
            403: openapi.Response(description="Forbidden"),
            500: openapi.Response(description="Internal Server Error"),
        },
        operation_id="list_unapproved_costs",
        operation_description="Retrieve list of unapproved cost entries with filtering options for status",
        manual_parameters=[
            openapi.Parameter(
                name="ready",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                description="Filter costs that are ready for approval (Under Review or Resubmit)",
                required=False,
                default=False,
            ),
            openapi.Parameter(
                name="approved",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                description="Filter costs that are already approved or deleted",
                required=False,
                default=False,
            ),
        ],
    )
    @check_permissionV2("RC-COST", PermissionSetV2.VIEW)
    def list(self, request, *args, **kwargs):

        try:
            filter_list = [
                UnapprovedCost.StatusChoices.PENDING,
                UnapprovedCost.StatusChoices.UNDER_REVIEW,
                UnapprovedCost.StatusChoices.RESUBMIT,
            ]
            pcm_id = self.kwargs.get("pcm_id")
            ready_for_approval = (
                request.query_params.get("ready", "false").lower() == "true"
            )
            approved = request.query_params.get("approved", "false").lower() == "true"

            user_id = request.user.user_id
            is_pm_user = False
            try:
                is_pm_user = is_pm(user_id, pcm_id)
            except Exception as e:
                is_pm_user = False

            if approved:
                filter_list = [
                    UnapprovedCost.StatusChoices.APPROVED,
                    UnapprovedCost.StatusChoices.DELETED,
                ]
            elif ready_for_approval:
                filter_list = [
                    UnapprovedCost.StatusChoices.UNDER_REVIEW,
                    UnapprovedCost.StatusChoices.RESUBMIT,
                ]

            if is_pm_user and not ready_for_approval:
                queryset = UnapprovedCost.objects.filter(
                    pcm_id=pcm_id, uac_status__in=filter_list
                ).order_by("-uac_added_on")
            elif ready_for_approval:
                # Filter by approver = current user if specified
                queryset = (
                    UnapprovedCost.objects.filter(
                        pcm_id=pcm_id, uac_status__in=filter_list
                    )
                    .filter(Q(cost_setup_id__pcs_approver=user_id))
                    .order_by("-uac_added_on")
                )
            else:
                queryset = (
                    UnapprovedCost.objects.filter(
                        pcm_id=pcm_id, uac_status__in=filter_list
                    )
                    .filter(
                        Q(cost_setup_id__pcs_approver=user_id)
                        | Q(cost_setup_id__pcs_created_by=user_id)
                    )
                    .order_by("-uac_added_on")
                )

            serializer = UnapprovedCostSerializer(queryset, many=True)
            return success_response(
                SUCCESS_MESSAGES["uac_fetched"], serializer.data, status.HTTP_200_OK
            )
        except Exception as e:
            tb = traceback.format_exc()
            logger.error(f"Exception occurred: {str(e)}\nTraceback: {tb}")
            return error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @swagger_auto_schema(
        tags=["Unapproved Cost"],
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Unapproved cost entry updated successfully.",
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "uac_id": openapi.Schema(type=openapi.TYPE_STRING),
                                "pcm_id": openapi.Schema(type=openapi.TYPE_STRING),
                                "cost_setup_id": openapi.Schema(
                                    type=openapi.TYPE_STRING, nullable=True
                                ),
                                "uac_actual_cost": openapi.Schema(
                                    type=openapi.TYPE_NUMBER
                                ),
                                "uac_status": openapi.Schema(type=openapi.TYPE_STRING),
                                "uac_added_on": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    format=openapi.FORMAT_DATETIME,
                                ),
                                "uac_updated_at": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    format=openapi.FORMAT_DATETIME,
                                ),
                                "uac_vendor": openapi.Schema(
                                    type=openapi.TYPE_STRING, nullable=True
                                ),
                                "uac_cost_code": openapi.Schema(
                                    type=openapi.TYPE_STRING, nullable=True
                                ),
                                "uac_cost_name": openapi.Schema(
                                    type=openapi.TYPE_STRING, nullable=True
                                ),
                                "uac_meta_data": openapi.Schema(
                                    type=openapi.TYPE_OBJECT
                                ),
                                "uac_qty": openapi.Schema(
                                    type=openapi.TYPE_NUMBER, nullable=True
                                ),
                            },
                        ),
                    },
                ),
            ),
            400: openapi.Response(
                description="Bad Request",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="error"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Cost entry already approved.",
                        ),
                    },
                ),
            ),
            401: openapi.Response(description="Unauthorized"),
            403: openapi.Response(description="Forbidden"),
            404: openapi.Response(description="Not Found"),
            500: openapi.Response(description="Internal Server Error"),
        },
        operation_id="update_unapproved_cost",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "uac_actual_cost": openapi.Schema(
                    type=openapi.TYPE_NUMBER, description="The actual cost amount"
                ),
                "uac_cost_code": openapi.Schema(
                    type=openapi.TYPE_STRING, description="Cost code"
                ),
                "uac_cost_name": openapi.Schema(
                    type=openapi.TYPE_STRING, description="Name of the cost"
                ),
                "uac_qty": openapi.Schema(
                    type=openapi.TYPE_NUMBER, description="Quantity"
                ),
                "uac_vendor": openapi.Schema(
                    type=openapi.TYPE_STRING, description="Vendor name"
                ),
                "pcs_id": openapi.Schema(
                    type=openapi.TYPE_STRING, description="Project cost setup ID"
                ),
            },
        ),
        operation_description="Update an existing unapproved cost entry that is in PENDING or RESUBMIT status",
    )
    @check_permissionV2("RC-COST", PermissionSetV2.UPDATE)
    def update(self, request, *args, **kwargs):
        try:
            uac_id = self.kwargs.get("uac_id")
            instance = UnapprovedCost.objects.get(uac_id=uac_id)
            user_id = request.user.user_id

            # Check if already approved
            if instance.uac_status in [
                UnapprovedCost.StatusChoices.APPROVED,
                UnapprovedCost.StatusChoices.DELETED,
                UnapprovedCost.StatusChoices.UNDER_REVIEW,
            ]:
                return error_response(
                    UNAPPROVED_COST_ERRORS["already_approved"],
                    status.HTTP_400_BAD_REQUEST,
                )

            data = request.data.copy()
            user_name = get_full_name(user_id)
            update_field = data.get("update_field", "")
            data_item = {
                "uac_updated_by": user_id,
                "uac_updated_on": timezone.now(),
                "uac_actual_cost": data.get(
                    "uac_actual_cost", instance.uac_actual_cost
                ),
                "uac_cost_code": data.get("uac_cost_code", instance.uac_cost_code),
                "uac_cost_name": data.get("uac_cost_name", instance.uac_cost_name),
                "uac_qty": data.get("uac_qty", instance.uac_qty),
                "uac_vendor": data.get("uac_vendor", instance.uac_vendor),
            }
            if "pcs_id" in data:
                pcs_id = data.get("pcs_id")
                data_item["cost_setup_id"] = pcs_id
                update_field = "pcs_id"

            old_value = ""
            log_value = f"Cost item has been updated by {user_name}."
            if update_field:
                old_value = getattr(instance, update_field, "")
                new_value = data_item.get(update_field, "")
                if update_field == "pcs_id":
                    log_value = activity_log_templates["config_linked"](
                        user_name,
                        data_item.get("uac_cost_code", ""),
                        data_item.get("uac_cost_name", ""),
                    )
                else:
                    field = activity_log_key_mapping_uac[update_field]
                    log_value = activity_log_templates["cost_update"](
                        user_name, field, old_value, new_value
                    )

            serializer = UnapprovedCostSerializer(
                instance, data=data_item, partial=True
            )
            if serializer.is_valid():
                serializer.save()
                pcm_id = getattr(instance.pcm_id, "pcm_id", "")
                event_payload = {
                    "pcm_id": pcm_id,
                    "obj_id": uac_id,
                    "obj_type": "unapprovedcost",
                    "user_id": user_id,
                    "syo_code": "RC-COST",
                    "request": request,
                    "to_users_pcm": [
                        {
                            "user_id": user_id,
                            "pcm_id": pcm_id,
                        }
                    ],
                    "log_action": log_value,
                }
                EventService(
                    payload=event_payload,
                )
                return success_response(
                    SUCCESS_MESSAGES["uac_updated"], serializer.data, status.HTTP_200_OK
                )

            # If serializer is not valid
            formatted_errors = format_serializer_errors(serializer.errors)
            return error_response(formatted_errors, status.HTTP_400_BAD_REQUEST)
        except UnapprovedCost.DoesNotExist:
            return error_response(
                UNAPPROVED_COST_ERRORS["not_found"], status.HTTP_404_NOT_FOUND
            )
        except ValidationError as ve:
            return error_response(str(ve), status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            tb = traceback.format_exc()
            logger.error(f"Exception occurred: {str(e)}\nTraceback: {tb}")
            return error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @swagger_auto_schema(
        tags=["Unapproved Cost"],
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Unapproved cost entry fetched successfully.",
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "uac_id": openapi.Schema(type=openapi.TYPE_STRING),
                                "pcm_id": openapi.Schema(type=openapi.TYPE_STRING),
                                "cost_setup_id": openapi.Schema(
                                    type=openapi.TYPE_STRING, nullable=True
                                ),
                                "uac_actual_cost": openapi.Schema(
                                    type=openapi.TYPE_NUMBER
                                ),
                                "uac_status": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    enum=[
                                        "PENDING",
                                        "UNDER_REVIEW",
                                        "APPROVED",
                                        "RESUBMIT",
                                        "DELETED",
                                    ],
                                ),
                                "uac_added_on": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    format=openapi.FORMAT_DATETIME,
                                ),
                                "uac_vendor": openapi.Schema(
                                    type=openapi.TYPE_STRING, nullable=True
                                ),
                                "uac_cost_code": openapi.Schema(
                                    type=openapi.TYPE_STRING, nullable=True
                                ),
                                "uac_cost_name": openapi.Schema(
                                    type=openapi.TYPE_STRING, nullable=True
                                ),
                                "uac_meta_data": openapi.Schema(
                                    type=openapi.TYPE_OBJECT
                                ),
                                "uac_qty": openapi.Schema(
                                    type=openapi.TYPE_NUMBER, nullable=True
                                ),
                                "added_by_details": openapi.Schema(
                                    type=openapi.TYPE_OBJECT, nullable=True
                                ),
                                "approved_by_details": openapi.Schema(
                                    type=openapi.TYPE_OBJECT, nullable=True
                                ),
                                "updated_by_details": openapi.Schema(
                                    type=openapi.TYPE_OBJECT, nullable=True
                                ),
                                "cost_setup_details": openapi.Schema(
                                    type=openapi.TYPE_OBJECT, nullable=True
                                ),
                            },
                        ),
                    },
                ),
            ),
            400: openapi.Response(description="Bad Request"),
            401: openapi.Response(description="Unauthorized"),
            403: openapi.Response(description="Forbidden"),
            404: openapi.Response(description="Not Found"),
            500: openapi.Response(description="Internal Server Error"),
        },
        operation_id="retrieve_unapproved_cost",
        operation_description="Retrieve details for a single unapproved cost entry by ID",
    )
    @check_permissionV2("RC-COST", PermissionSetV2.VIEW)
    def retrieve(self, request, *args, **kwargs):
        try:
            uac_id = self.kwargs.get("uac_id")
            instance = UnapprovedCost.objects.get(uac_id=uac_id)

            serializer = UnapprovedCostSerializer(instance)
            return success_response(
                SUCCESS_MESSAGES["uac_fetched"], serializer.data, status.HTTP_200_OK
            )
        except UnapprovedCost.DoesNotExist:
            return error_response(
                UNAPPROVED_COST_ERRORS["not_found"], status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            tb = traceback.format_exc()
            logger.error(f"Exception occurred: {str(e)}\nTraceback: {tb}")
            return error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @swagger_auto_schema(
        tags=["Unapproved Cost"],
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Unapproved cost entry deleted successfully.",
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "uac_id": openapi.Schema(type=openapi.TYPE_STRING),
                                "pcm_id": openapi.Schema(type=openapi.TYPE_STRING),
                                "uac_status": openapi.Schema(
                                    type=openapi.TYPE_STRING, example="DELETED"
                                ),
                                "uac_updated_by": openapi.Schema(
                                    type=openapi.TYPE_STRING
                                ),
                                "uac_updated_at": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    format=openapi.FORMAT_DATETIME,
                                ),
                            },
                        ),
                    },
                ),
            ),
            400: openapi.Response(
                description="Bad Request",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="error"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Cannot delete an approved cost entry.",
                        ),
                    },
                ),
            ),
            401: openapi.Response(description="Unauthorized"),
            403: openapi.Response(description="Forbidden"),
            404: openapi.Response(description="Not Found"),
            500: openapi.Response(description="Internal Server Error"),
        },
        operation_id="destroy_unapproved_cost",
        operation_description="Delete an unapproved cost entry (marks it as DELETED)",
    )
    @check_permissionV2("RC-COST", PermissionSetV2.DELETE)
    def destroy(self, request, *args, **kwargs):
        try:
            uac_id = self.kwargs.get("uac_id")
            instance = UnapprovedCost.objects.get(uac_id=uac_id)
            user_id = request.user.user_id
            # Check if already approved
            if instance.uac_status == UnapprovedCost.StatusChoices.APPROVED:
                return error_response(
                    UNAPPROVED_COST_ERRORS["invalid_delete"],
                    status.HTTP_400_BAD_REQUEST,
                )

            instance.uac_status = UnapprovedCost.StatusChoices.DELETED
            instance.uac_updated_by = user_id
            instance.uac_updated_at = timezone.now()
            instance.save()

            user_name = get_full_name(user_id)
            pcm_id = getattr(instance.pcm_id, "pcm_id", "")
            event_payload = {
                "pcm_id": pcm_id,
                "obj_id": uac_id,
                "obj_type": "unapprovedcost",
                "user_id": user_id,
                "syo_code": "RC-COST",
                "request": request,
                "to_users_pcm": [
                    {
                        "user_id": user_id,
                        "pcm_id": pcm_id,
                    }
                ],
                "log_action": activity_log_templates["cost_delete"](user_name),
            }
            EventService(
                payload=event_payload,
            )

            serializer = UnapprovedCostSerializer(instance)
            return success_response(
                SUCCESS_MESSAGES["uac_deleted"], serializer.data, status.HTTP_200_OK
            )
        except UnapprovedCost.DoesNotExist:
            return error_response(
                UNAPPROVED_COST_ERRORS["not_found"], status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            tb = traceback.format_exc()
            logger.error(f"Exception occurred: {str(e)}\nTraceback: {tb}")
            return error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @swagger_auto_schema(
        tags=["Unapproved Cost"],
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Unapproved cost entries submitted successfully.",
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "count": openapi.Schema(
                                    type=openapi.TYPE_INTEGER,
                                    description="Number of cost entries submitted",
                                ),
                                "uac_ids": openapi.Schema(
                                    type=openapi.TYPE_ARRAY,
                                    items=openapi.Schema(type=openapi.TYPE_STRING),
                                ),
                            },
                        ),
                    },
                ),
            ),
            400: openapi.Response(
                description="Bad Request",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="error"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING, example="No cost IDs provided."
                        ),
                    },
                ),
            ),
            401: openapi.Response(description="Unauthorized"),
            403: openapi.Response(description="Forbidden"),
            404: openapi.Response(description="Not Found"),
            500: openapi.Response(description="Internal Server Error"),
        },
        operation_id="submit_unapproved_costs",
        operation_description="Submit multiple unapproved cost entries for review. Changes status from PENDING or RESUBMIT to UNDER_REVIEW.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["uac_ids"],
            properties={
                "uac_ids": openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_STRING),
                    description="List of unapproved cost IDs to submit for review",
                )
            },
        ),
    )
    # This method allows users to submit or resubmit multiple unapproved costs for approvel.
    @check_permissionV2("RC-COST", PermissionSetV2.SUBMIT)
    def submit_many(self, request, *args, **kwargs):
        uac_ids = request.data.get("uac_ids", [])
        pcm_id = self.kwargs.get("pcm_id")
        if not uac_ids:
            return error_response("No cost IDs provided.", status.HTTP_400_BAD_REQUEST)
        if not isinstance(uac_ids, list):
            return error_response(
                "uac_ids must be an array of IDs.", status.HTTP_400_BAD_REQUEST
            )
        return self.status_change(request, "submit", pcm_id)

    @swagger_auto_schema(
        tags=["Unapproved Cost"],
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Unapproved cost entries revised successfully.",
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "count": openapi.Schema(
                                    type=openapi.TYPE_INTEGER,
                                    description="Number of cost entries revised",
                                ),
                                "uac_ids": openapi.Schema(
                                    type=openapi.TYPE_ARRAY,
                                    items=openapi.Schema(type=openapi.TYPE_STRING),
                                ),
                            },
                        ),
                    },
                ),
            ),
            400: openapi.Response(
                description="Bad Request",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="error"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING, example="No cost IDs provided."
                        ),
                    },
                ),
            ),
            401: openapi.Response(description="Unauthorized"),
            403: openapi.Response(description="Forbidden"),
            404: openapi.Response(description="Not Found"),
            500: openapi.Response(description="Internal Server Error"),
        },
        operation_id="revise_unapproved_costs",
        operation_description="Revise unapproved cost entries for resubmission. Changes status from UNDER_REVIEW to RESUBMIT.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["uac_ids"],
            properties={
                "uac_ids": openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_STRING),
                    description="List of unapproved cost IDs to revise",
                )
            },
        ),
    )
    @check_permissionV2("RC-COST", PermissionSetV2.REVISE)
    def revise_many(self, request, *args, **kwargs):
        uac_ids = request.data.get("uac_ids", [])
        pcm_id = self.kwargs.get("pcm_id")
        if not uac_ids:
            return error_response("No cost IDs provided.", status.HTTP_400_BAD_REQUEST)
        if not isinstance(uac_ids, list):
            return error_response(
                "uac_ids must be an array of IDs.", status.HTTP_400_BAD_REQUEST
            )
        return self.status_change(request, "revise", pcm_id)

    def status_change(self, request, action, pcm_id):
        uac_ids = request.data.get("uac_ids", [])
        try:
            with transaction.atomic():
                filter_list = []
                status_change_to = UnapprovedCost.StatusChoices.UNDER_REVIEW
                success_messages = SUCCESS_MESSAGES["uac_submitted"]
                if action == "submit":
                    filter_list = [
                        UnapprovedCost.StatusChoices.PENDING,
                        UnapprovedCost.StatusChoices.RESUBMIT,
                    ]
                    status_change_to = UnapprovedCost.StatusChoices.UNDER_REVIEW
                    success_messages = SUCCESS_MESSAGES["uac_submitted"]
                elif action == "revise":
                    filter_list = [
                        UnapprovedCost.StatusChoices.UNDER_REVIEW,
                    ]
                    status_change_to = UnapprovedCost.StatusChoices.RESUBMIT
                    success_messages = SUCCESS_MESSAGES["uac_revised"]

                unapproved_costs = UnapprovedCost.objects.filter(
                    uac_id__in=uac_ids,
                    pcm_id=pcm_id,
                    uac_status__in=filter_list,
                )
                if unapproved_costs.count() == 0:
                    return error_response(
                        "No unapproved costs found for the provided IDs.",
                        status.HTTP_404_NOT_FOUND,
                    )

                user = company_employee.objects.get(user_id=request.user.user_id)
                now = timezone.now()
                for cost in unapproved_costs:
                    cost.uac_status = status_change_to
                    cost.uac_updated_by = user
                    cost.uac_updated_at = now

                # Bulk update
                UnapprovedCost.objects.bulk_update(
                    unapproved_costs, ["uac_status", "uac_updated_by", "uac_updated_at"]
                )
            return success_response(
                success_messages,
                {
                    "count": len(unapproved_costs),
                    "uac_ids": uac_ids,
                },
                status.HTTP_200_OK,
            )
        except company_employee.DoesNotExist:
            return error_response(
                RECURRING_COST_ERRORS["user_not_found"], status.HTTP_404_NOT_FOUND
            )
        except ValidationError as ve:
            return error_response(str(ve), status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            tb = traceback.format_exc()
            logger.error(f"Exception occurred: {str(e)}\nTraceback: {tb}")
            return error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @swagger_auto_schema(
        tags=["Unapproved Cost"],
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Unapproved cost entries approved successfully.",
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "count": openapi.Schema(
                                    type=openapi.TYPE_INTEGER,
                                    description="Number of cost entries approved",
                                ),
                                "uac_ids": openapi.Schema(
                                    type=openapi.TYPE_ARRAY,
                                    items=openapi.Schema(type=openapi.TYPE_STRING),
                                ),
                            },
                        ),
                    },
                ),
            ),
            400: openapi.Response(
                description="Bad Request",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="error"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING, example="No cost IDs provided."
                        ),
                    },
                ),
            ),
            401: openapi.Response(description="Unauthorized"),
            403: openapi.Response(description="Forbidden"),
            404: openapi.Response(description="Not Found"),
            500: openapi.Response(description="Internal Server Error"),
        },
        operation_id="bulk_approve_unapproved_costs",
        operation_description="Approve multiple unapproved cost entries in bulk. Changes status to APPROVED.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["uac_ids"],
            properties={
                "uac_ids": openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_STRING),
                    description="List of unapproved cost IDs to approve",
                )
            },
        ),
    )
    @check_permissionV2("RC-COST", PermissionSetV2.APPROVE)
    def bulk_approve(self, request, *args, **kwargs):
        try:
            uac_ids = request.data.get("uac_ids", [])
            if not uac_ids:
                return error_response(
                    "No cost IDs provided.", status.HTTP_400_BAD_REQUEST
                )

            if not isinstance(uac_ids, list):
                return error_response(
                    "cost_ids must be an array of IDs.",
                    status.HTTP_400_BAD_REQUEST,
                )

            with transaction.atomic():
                unapproved_costs = UnapprovedCost.objects.filter(
                    uac_id__in=uac_ids,
                    uac_status__in=[
                        UnapprovedCost.StatusChoices.PENDING,
                        UnapprovedCost.StatusChoices.UNDER_REVIEW,
                    ],
                )
                if unapproved_costs.count() == 0:
                    return error_response(
                        "No unapproved costs found for the provided IDs.",
                        status.HTTP_404_NOT_FOUND,
                    )
                cost_setups = {cost.cost_setup_id for cost in unapproved_costs}
                cost_setup_map = {setup.pcs_id: setup for setup in cost_setups}
                validated_instances = {
                    ProjectCostSetup.CostRelatesToChoices.OTHER: [],
                    ProjectCostSetup.CostRelatesToChoices.OVERHEAD: [],
                    ProjectCostSetup.CostRelatesToChoices.MATERIAL: [],
                }
                # Prepare instances for each cost relates to type
                now = timezone.now()
                for cost in unapproved_costs:
                    cost_setup = cost_setup_map[cost.cost_setup_id.pcs_id]
                    cost_type = cost_setup.pcs_cost_relates_to
                    serializer_class = CostRelatesToSerializer[cost_type]
                    payload = get_payload(cost_type, model_to_dict(cost), now)

                    serializer = serializer_class(data=payload)
                    serializer.is_valid(raise_exception=True)

                    model_class = serializer.Meta.model  # ✅ get model from serializer
                    validated_instances[cost_type].append(
                        model_class(**serializer.validated_data)
                    )

                # Now save all validated instances in bulk
                if validated_instances[ProjectCostSetup.CostRelatesToChoices.MATERIAL]:
                    SmcSerializerPost.Meta.model.objects.bulk_create(
                        validated_instances[
                            ProjectCostSetup.CostRelatesToChoices.MATERIAL
                        ]
                    )

                if validated_instances[ProjectCostSetup.CostRelatesToChoices.OTHER]:
                    SOCSerializer.Meta.model.objects.bulk_create(
                        validated_instances[ProjectCostSetup.CostRelatesToChoices.OTHER]
                    )

                if validated_instances[ProjectCostSetup.CostRelatesToChoices.OVERHEAD]:
                    SOCSerializer.Meta.model.objects.bulk_create(
                        validated_instances[
                            ProjectCostSetup.CostRelatesToChoices.OVERHEAD
                        ]
                    )

                user = company_employee.objects.get(user_id=request.user.user_id)
                for cost in unapproved_costs:
                    cost.uac_status = UnapprovedCost.StatusChoices.APPROVED
                    cost.uac_approved_by = user
                    cost.uac_approved_on = now

                # Bulk update
                UnapprovedCost.objects.bulk_update(
                    unapproved_costs,
                    ["uac_status", "uac_approved_by", "uac_approved_on"],
                )
                # Create Signature
                # First get employee signature
                user_id = request.user.user_id
                employee_signature = None
                try:
                    employee_signature = EmployeeSignature.objects.get(
                        employee=user_id, is_active=True
                    )
                except EmployeeSignature.DoesNotExist:
                    return error_response(
                        UNAPPROVED_COST_ERRORS["signature_not_found"],
                        status.HTTP_404_NOT_FOUND,
                    )
                # Create ObjectSignature for each approved cost
                object_signatures = []
                object_type = UnapprovedCost._meta.model_name

                content_type = ContentType.objects.get(model=object_type)
                for uac_id in uac_ids:
                    object_signature = ObjectSignature(
                        object_id=uac_id,
                        object_type=content_type,
                        signature=employee_signature,
                        created_by=user,
                        created_on=now,
                    )
                    object_signatures.append(object_signature)
                # Bulk create ObjectSignatures
                ObjectSignature.objects.bulk_create(object_signatures)

            return success_response(
                SUCCESS_MESSAGES["uac_bulk_approved"],
                {"approved_count": len(unapproved_costs), "approved_ids": uac_ids},
                status.HTTP_200_OK,
            )
        except company_employee.DoesNotExist:
            return error_response(
                RECURRING_COST_ERRORS["user_not_found"], status.HTTP_404_NOT_FOUND
            )
        except ValidationError as ve:
            return error_response(str(ve), status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            tb = traceback.format_exc()
            logger.error(f"Exception occurred: {str(e)}\nTraceback: {tb}")
            return error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @swagger_auto_schema(
        tags=["Unapproved Cost"],
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Unapproved cost entries uploaded successfully.",
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "count": openapi.Schema(
                                    type=openapi.TYPE_INTEGER,
                                    description="Number of cost entries created",
                                ),
                                "uac_ids": openapi.Schema(
                                    type=openapi.TYPE_ARRAY,
                                    items=openapi.Schema(type=openapi.TYPE_STRING),
                                ),
                            },
                        ),
                    },
                ),
            ),
            400: openapi.Response(
                description="Bad Request",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="error"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING, example="Invalid data provided."
                        ),
                    },
                ),
            ),
            401: openapi.Response(description="Unauthorized"),
            403: openapi.Response(description="Forbidden"),
            404: openapi.Response(description="Not Found"),
            500: openapi.Response(description="Internal Server Error"),
        },
        operation_id="bulk_upload_unapproved_costs",
        operation_description="Upload multiple unapproved cost entries in bulk",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=[
                "cost_code",
                "cost_name",
                "total_cost",
                "vendor",
                "qty",
                "cost_entries",
            ],
            properties={
                "cost_code": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Cost code for the unapproved cost",
                ),
                "cost_name": openapi.Schema(
                    type=openapi.TYPE_STRING, description="Name of the cost"
                ),
                "total_cost": openapi.Schema(
                    type=openapi.TYPE_NUMBER, description="Total cost of all entries"
                ),
                "vendor": openapi.Schema(
                    type=openapi.TYPE_STRING, description="Vendor name"
                ),
                "qty": openapi.Schema(
                    type=openapi.TYPE_NUMBER, description="Total quantity"
                ),
                "receipt_no": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Receipt number",
                    nullable=True,
                ),
                "cost_entries": openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        properties={
                            "name": openapi.Schema(
                                type=openapi.TYPE_STRING,
                                description="Name of the cost entry",
                            ),
                            "uom": openapi.Schema(
                                type=openapi.TYPE_STRING,
                                description="Unit of measurement",
                            ),
                            "unit_rate": openapi.Schema(
                                type=openapi.TYPE_NUMBER, description="Cost per unit"
                            ),
                            "qty": openapi.Schema(
                                type=openapi.TYPE_NUMBER, description="Quantity"
                            ),
                        },
                    ),
                ),
            },
        ),
    )
    @check_permissionV2("RC-COST", PermissionSetV2.CREATE)
    def bulk_upload(self, request, *args, **kwargs):
        """
        Bulk upload method for unapproved costs.
        Accepts:
        - cost_code: Cost code for the unapproved cost
        - cost_name: Name of the cost
        - total_cost: Total cost of all entries
        - vendor: Vendor name
        - qty: Total quantity
        - receipt_no: Receipt number (optional)
        - cost_entries: List of cost entry objects with name, uom, unit_rate, qty

        Returns:
            Response: Success or error response
        """
        try:
            pcm_id = self.kwargs.get("pcm_id")
            body = request.data.copy()
            cost_entries = body.get("cost_entries", [])
            user_id = request.user.user_id
            # Validate cost entries
            if not cost_entries:
                return error_response(
                    "No cost entries provided.", status.HTTP_400_BAD_REQUEST
                )
            if not isinstance(cost_entries, list):
                return error_response(
                    "cost_entries must be an array of entries.",
                    status.HTTP_400_BAD_REQUEST,
                )

            # Prepare metadata
            meta_data = format_unapproved_cost_meta_data(
                {"receipt_no": body.get("receipt_no", ""), "cost_entries": cost_entries}
            )

            # Calculate total cost if not provided
            total_cost = body.get("total_cost", 0)
            if not total_cost:
                total_cost = sum(
                    entry.get("unit_rate", 0) * entry.get("qty", 0)
                    for entry in cost_entries
                )

            # Calculate total quantity if not provided
            total_qty = body.get("qty", 0)
            if not total_qty:
                total_qty = sum(entry.get("qty", 0) for entry in cost_entries)
            with transaction.atomic():
                current_time = timezone.now()
                user = company_employee.objects.get(user_id=user_id)
                # Create unapproved cost entry
                unapproved_cost = UnapprovedCost(
                    pcm_id_id=pcm_id,
                    uac_actual_cost=total_cost,
                    uac_vendor=body.get("vendor", ""),
                    uac_qty=total_qty,
                    uac_added_on=current_time,
                    uac_added_by=user,
                    uac_meta_data=meta_data,
                    uac_status=UnapprovedCost.StatusChoices.PENDING,
                )
                # Set cost setup if provided
                # I need to link the cost setup if cost_code
                if "cost_code" in body:
                    # then source, cost_relates_to
                    pcs = ProjectCostSetup.objects.filter(
                        pcs_cost_code=body.get("cost_code", ""),
                        pcs_status=ProjectCostSetup.StatusChoices.ACCEPTED,
                        pcs_is_active=True,
                        pcs_frequency=ProjectCostSetup.FrequencyChoices.ADHOC,
                        pcm_id=pcm_id,
                        pcs_cost_relates_to=body.get("cost_relates", None),
                        pcs_cost_source=body.get("source", None),
                    ).first()
                    unapproved_cost.uac_cost_code = body.get("cost_code", "")
                    unapproved_cost.uac_cost_name = body.get("cost_name", "")
                    if pcs:
                        unapproved_cost.cost_setup_id = pcs
                    else:
                        meta_data["has_error"] = True
                        unapproved_cost.uac_meta_data = meta_data

                unapproved_cost.save()
                # Notification sending
                user_name = f"{user.emp_first_name} {user.emp_last_name}"
                event_payload = {
                    "pcm_id": pcm_id,
                    "obj_id": unapproved_cost.uac_id,
                    "obj_type": "unapprovedcost",
                    "user_id": user_id,
                    "syo_code": "RC-COST",
                    "request": request,
                    "to_users_pcm": [
                        {
                            "user_id": user_id,
                            "pcm_id": pcm_id,
                        }
                    ],
                    "log_action": activity_log_templates["cost_uploaded"](
                        user_name, len(cost_entries)
                    ),
                }
                EventService(
                    payload=event_payload,
                )
                # Extract names for the response
                cost_entry_names = [
                    entry.get("name", f"Item {idx+1}")
                    for idx, entry in enumerate(cost_entries)
                ]
                return success_response(
                    SUCCESS_MESSAGES["uac_created"],
                    {
                        "uac_id": unapproved_cost.uac_id,
                        "total_cost": total_cost,
                        "total_qty": total_qty,
                        "vendor": body.get("vendor", ""),
                        "cost_entries": cost_entry_names,
                        "receipt_no": body.get("receipt_no", ""),
                    },
                    status.HTTP_201_CREATED,
                )
        except company_employee.DoesNotExist:
            return error_response(
                RECURRING_COST_ERRORS["user_not_found"], status.HTTP_404_NOT_FOUND
            )
        except ValidationError as ve:
            return error_response(str(ve), status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            tb = traceback.format_exc()
            logger.error(f"Exception occurred: {str(e)}\nTraceback: {tb}")
            return error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @swagger_auto_schema(
        tags=["Unapproved Cost"],
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Unapproved cost entries deleted successfully.",
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "count": openapi.Schema(
                                    type=openapi.TYPE_INTEGER,
                                    description="Number of cost entries deleted",
                                ),
                                "uac_ids": openapi.Schema(
                                    type=openapi.TYPE_ARRAY,
                                    items=openapi.Schema(type=openapi.TYPE_STRING),
                                ),
                            },
                        ),
                    },
                ),
            ),
            400: openapi.Response(
                description="Bad Request",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="error"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING, example="No cost IDs provided."
                        ),
                    },
                ),
            ),
            401: openapi.Response(description="Unauthorized"),
            403: openapi.Response(description="Forbidden"),
            404: openapi.Response(description="Not Found"),
            500: openapi.Response(description="Internal Server Error"),
        },
        operation_id="bulk_delete_unapproved_costs",
        operation_description="Mark multiple unapproved cost entries as deleted in bulk",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["uac_ids"],
            properties={
                "uac_ids": openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_STRING),
                    description="List of unapproved cost IDs to delete",
                )
            },
        ),
    )
    @check_permissionV2("RC-COST", PermissionSetV2.DELETE)
    def bulk_delete(self, request, *args, **kwargs):
        try:
            uac_ids = request.data.get("uac_ids", [])
            user_id = request.user.user_id
            if not uac_ids:
                return error_response(
                    "No cost IDs provided.", status.HTTP_400_BAD_REQUEST
                )

            if not isinstance(uac_ids, list):
                return error_response(
                    "uac_ids must be an array of IDs.",
                    status.HTTP_400_BAD_REQUEST,
                )

            with transaction.atomic():
                unapproved_costs = UnapprovedCost.objects.filter(
                    uac_id__in=uac_ids,
                    uac_status__in=[
                        UnapprovedCost.StatusChoices.PENDING,
                        UnapprovedCost.StatusChoices.UNDER_REVIEW,
                    ],
                )

                if unapproved_costs.count() == 0:
                    return error_response(
                        "No eligible unapproved costs found for deletion.",
                        status.HTTP_404_NOT_FOUND,
                    )

                # Check if any costs are approved (we won't delete these)
                approved_costs = UnapprovedCost.objects.filter(
                    uac_id__in=uac_ids,
                    uac_status=UnapprovedCost.StatusChoices.APPROVED,
                )

                approved_ids = [cost.uac_id for cost in approved_costs]

                # Update status to DELETED for eligible costs
                user = company_employee.objects.get(user_id=user_id)
                for cost in unapproved_costs:
                    cost.uac_status = UnapprovedCost.StatusChoices.DELETED
                    cost.uac_updated_by = user

                # Bulk update
                UnapprovedCost.objects.bulk_update(
                    unapproved_costs, ["uac_status", "uac_updated_by"]
                )
                if len(uac_ids) == 1:
                    try:
                        user_name = get_full_name(user_id)
                        pcm_id = getattr(unapproved_costs[0].pcm_id, "pcm_id", "")
                        uac_id = uac_ids[0]
                        event_payload = {
                            "pcm_id": pcm_id,
                            "obj_id": uac_id,
                            "obj_type": "unapprovedcost",
                            "user_id": user_id,
                            "syo_code": "RC-COST",
                            "request": request,
                            "to_users_pcm": [
                                {
                                    "user_id": user_id,
                                    "pcm_id": pcm_id,
                                }
                            ],
                            "log_action": activity_log_templates["cost_delete"](
                                user_name
                            ),
                        }
                        EventService(
                            payload=event_payload,
                        )
                    except Exception as e:
                        tb = traceback.format_exc()
                        logger.error(f"Exception occurred: {str(e)}\nTraceback: {tb}")

            return success_response(
                SUCCESS_MESSAGES["uac_bulk_deleted"],
                {
                    "deleted_count": len(unapproved_costs),
                    "deleted_ids": [str(cost.uac_id) for cost in unapproved_costs],
                    "skipped_approved_ids": approved_ids if approved_ids else [],
                },
                status.HTTP_200_OK,
            )
        except company_employee.DoesNotExist:
            return error_response(
                RECURRING_COST_ERRORS["user_not_found"], status.HTTP_404_NOT_FOUND
            )
        except ValidationError as ve:
            return error_response(str(ve), status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            tb = traceback.format_exc()
            logger.error(f"Exception occurred: {str(e)}\nTraceback: {tb}")
            return error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @check_permissionV2("RC-COST", PermissionSetV2.VIEW)
    def uploaded_lists(self, request, *args, **kwargs):
        """
        Fetch all uploaded unapproved costs for a given pcm_id.
        """
        try:
            pcm_id = self.kwargs.get("pcm_id")
            queryset = (
                UnapprovedCost.objects.filter(
                    pcm_id=pcm_id,
                    uac_added_by__isnull=False,
                )
                .select_related(
                    "uac_added_by",
                    "uac_updated_by",
                    "uac_approved_by",
                    "cost_setup_id",
                    "cost_setup_id__pcs_created_by",
                    "cost_setup_id__pcs_approver",
                )
                .order_by("-uac_added_on")
            )
            serializer = UnapprovedCostSerializer(queryset, many=True)
            return success_response(
                SUCCESS_MESSAGES["uac_fetched"], serializer.data, status.HTTP_200_OK
            )
        except Exception as e:
            tb = traceback.format_exc()
            logger.error(f"Exception occurred: {str(e)}\nTraceback: {tb}")
            return error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
