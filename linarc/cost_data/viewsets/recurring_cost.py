from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from linarc.utils.swagger_utils import get_response_schema
from rest_framework import viewsets, status
from companies.models import company_employee
from ..models import ProjectCostSetup
from ..serializers import ProjectCostSetupSerializer
from django.core.exceptions import ValidationError
from django.db import IntegrityError
import re
from ..utils import (
    RECURRING_COST_ERRORS,
    SUCCESS_MESSAGES,
    error_response,
    success_response,
    default_response_body,
    get_user_having_permission,
    get_full_name,
    activity_log_key_mapping_rc,
)
from django.utils import timezone
from rbac.decorators import PermissionSetV2, check_permissionV2
from projects.services import activity_log_templates
from notification.notification_v2_services import EventService


def format_serializer_errors(errors):
    for field, field_errors in errors.items():
        if field_errors:
            raw_message = str(field_errors[0])
            # Extract invalid value from the message (e.g., '"kj" is not a valid choice.')
            match = re.match(r'"(.*)" is not a valid choice\.', raw_message)
            if match:
                invalid_value = match.group(1)
                return f"{field} <{invalid_value}> is not a valid choice."
            return f"{field}: {raw_message}"
    return "Invalid input."


class RecurringCostViewSet(viewsets.ViewSet):

    @swagger_auto_schema(
        tags=["Recurring Cost"],
        responses={
            200: openapi.Response(
                description="API is operational",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Recurring Cost API is operational.",
                        ),
                        "data": openapi.Schema(type=openapi.TYPE_OBJECT, nullable=True),
                    },
                ),
            ),
            400: openapi.Response(description="Bad Request"),
            401: openapi.Response(description="Unauthorized"),
            403: openapi.Response(description="Forbidden"),
            500: openapi.Response(description="Internal Server Error"),
        },
        operation_id="health_check",
        operation_description="Health check endpoint for RecurringCost API",
    )
    def health_check(self, request):
        """
        Health check endpoint for the recurring cost API.
        Returns a 200 OK response if the API is operational.
        """
        return success_response(
            SUCCESS_MESSAGES["health"], None, status=status.HTTP_200_OK
        )

    @swagger_auto_schema(
        tags=["Recurring Cost", "Configuration"],
        responses={
            201: openapi.Response(
                description="Created",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Cost configuration created successfully.",
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "pcs_id": openapi.Schema(type=openapi.TYPE_STRING),
                                "pcm_id": openapi.Schema(type=openapi.TYPE_STRING),
                                "prj_id": openapi.Schema(type=openapi.TYPE_STRING),
                                "pcs_cost_source": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    enum=[
                                        "FIELD",
                                        "OFFICE",
                                        "ADMINISTRATIVE",
                                        "FINANCE",
                                    ],
                                ),
                                "pcs_cost_type": openapi.Schema(
                                    type=openapi.TYPE_STRING, enum=["FIX", "VAR"]
                                ),
                                "pcs_cost_attribution": openapi.Schema(
                                    type=openapi.TYPE_STRING, enum=["DIR", "IND"]
                                ),
                                "pcs_cost_relates_to": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    enum=["MAT", "EQU", "LAB", "SUB", "OT", "OH"],
                                ),
                                "pcs_cost_code": openapi.Schema(
                                    type=openapi.TYPE_STRING
                                ),
                                "pcs_cost_name": openapi.Schema(
                                    type=openapi.TYPE_STRING
                                ),
                                "pcs_rate": openapi.Schema(
                                    type=openapi.TYPE_NUMBER, nullable=True
                                ),
                                "pcs_frequency": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    enum=[
                                        "ADHOC",
                                        "DAILY",
                                        "WEEKLY",
                                        "BIWEEKLY",
                                        "MONTHLY",
                                        "QUARTERLY",
                                        "YEARLY",
                                    ],
                                ),
                                "pcs_start_date": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    format=openapi.FORMAT_DATE,
                                    nullable=True,
                                ),
                                "pcs_end_date": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    format=openapi.FORMAT_DATE,
                                    nullable=True,
                                ),
                                "pcs_is_active": openapi.Schema(
                                    type=openapi.TYPE_BOOLEAN
                                ),
                                "pcs_status": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    enum=[
                                        "OPEN",
                                        "ACCEPTED",
                                        "CLOSED",
                                        "REVISE",
                                        "DELETED",
                                    ],
                                ),
                                "pcs_created_on": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    format=openapi.FORMAT_DATETIME,
                                ),
                                "approver_details": openapi.Schema(
                                    type=openapi.TYPE_OBJECT, nullable=True
                                ),
                                "created_by_details": openapi.Schema(
                                    type=openapi.TYPE_OBJECT, nullable=True
                                ),
                                "deactivated_by_details": openapi.Schema(
                                    type=openapi.TYPE_OBJECT, nullable=True
                                ),
                            },
                        ),
                    },
                ),
            ),
            400: openapi.Response(
                description="Bad Request",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="error"
                        ),
                        "message": openapi.Schema(type=openapi.TYPE_STRING),
                    },
                ),
            ),
            401: openapi.Response(description="Unauthorized"),
            403: openapi.Response(description="Forbidden"),
            500: openapi.Response(description="Internal Server Error"),
        },
        operation_id="create_recurring_cost_config",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=[
                "pcm_id",
                "prj_id",
                "pcs_cost_source",
                "pcs_cost_type",
                "pcs_cost_attribution",
                "pcs_cost_relates_to",
                "pcs_cost_code",
                "pcs_cost_name",
                "pcs_frequency",
            ],
            properties={
                "pcm_id": openapi.Schema(
                    type=openapi.TYPE_STRING, description="Project company mapping ID"
                ),
                "prj_id": openapi.Schema(
                    type=openapi.TYPE_STRING, description="Project ID"
                ),
                "pcs_cost_source": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    enum=["FIELD", "OFFICE", "ADMINISTRATIVE", "FINANCE"],
                    description="Source of the cost",
                ),
                "pcs_cost_type": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    enum=["FIX", "VAR"],
                    description="Fixed or variable cost",
                ),
                "pcs_cost_attribution": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    enum=["DIR", "IND"],
                    description="Direct or indirect cost attribution",
                ),
                "pcs_cost_relates_to": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    enum=["MAT", "EQU", "LAB", "SUB", "OT", "OH"],
                    description="What the cost relates to",
                ),
                "pcs_cost_code": openapi.Schema(
                    type=openapi.TYPE_STRING, description="Cost code"
                ),
                "pcs_cost_name": openapi.Schema(
                    type=openapi.TYPE_STRING, description="Name of the cost"
                ),
                "pcs_rate": openapi.Schema(
                    type=openapi.TYPE_NUMBER,
                    description="Rate for the cost",
                    nullable=True,
                ),
                "pcs_frequency": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    enum=[
                        "ADHOC",
                        "DAILY",
                        "WEEKLY",
                        "BIWEEKLY",
                        "MONTHLY",
                        "QUARTERLY",
                        "YEARLY",
                    ],
                    description="Frequency of the cost",
                ),
                "pcs_start_date": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    format=openapi.FORMAT_DATE,
                    description="Start date for recurring cost",
                    nullable=True,
                ),
                "pcs_end_date": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    format=openapi.FORMAT_DATE,
                    description="End date for recurring cost",
                    nullable=True,
                ),
                "pcs_vendor": openapi.Schema(
                    type=openapi.TYPE_STRING, description="Vendor name", nullable=True
                ),
                "pcs_approver": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Approver user ID",
                    nullable=True,
                ),
            },
        ),
        operation_description="Create a new recurring cost configuration setup",
    )
    @check_permissionV2("RC-SETUP", PermissionSetV2.CREATE)
    def create(self, request, *args, **kwargs):
        try:
            data = request.data.copy()
            user_id = request.user.user_id
            pcm_id = self.kwargs.get("pcm_id")
            data["pcs_created_by"] = user_id
            serializer = ProjectCostSetupSerializer(data=data)
            if serializer.is_valid():
                serializer.save()
                user_name = get_full_name(user_id)
                event_payload = {
                    "pcm_id": pcm_id,
                    "obj_id": serializer.data.get("pcs_id"),
                    "obj_type": "projectcostsetup",
                    "event": "rc_config_added",
                    "user_id": user_id,
                    "syo_code": "RC-SETUP",
                    "request": request,
                    "to_users_pcm": [
                        {
                            "user_id": user_id,
                            "pcm_id": pcm_id,
                        }
                    ],
                    "log_action": activity_log_templates["rc_create"](user_name),
                }
                EventService(
                    payload=event_payload,
                    notification=True,
                )
                return success_response(
                    SUCCESS_MESSAGES["created"],
                    serializer.data,
                    status.HTTP_201_CREATED,
                )

            # If serializer is not valid
            formatted_errors = format_serializer_errors(serializer.errors)
            return error_response(formatted_errors, status.HTTP_400_BAD_REQUEST)
        except ValidationError as ve:
            return error_response(str(ve), status.HTTP_400_BAD_REQUEST)
        except IntegrityError as e:
            if "unique_adhoc_cost_entry_when_not_deleted" in str(e):
                return error_response(
                    RECURRING_COST_ERRORS["duplicate_config"],
                    status.HTTP_400_BAD_REQUEST,
                )
            return error_response(str(e), status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @swagger_auto_schema(
        tags=["Recurring Cost"],
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Cost configuration fetched successfully.",
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_ARRAY,
                            items=openapi.Schema(
                                type=openapi.TYPE_OBJECT,
                                properties={
                                    "pcs_id": openapi.Schema(type=openapi.TYPE_STRING),
                                    "pcm_id": openapi.Schema(type=openapi.TYPE_STRING),
                                    "prj_id": openapi.Schema(type=openapi.TYPE_STRING),
                                    "pcs_cost_source": openapi.Schema(
                                        type=openapi.TYPE_STRING
                                    ),
                                    "pcs_cost_type": openapi.Schema(
                                        type=openapi.TYPE_STRING
                                    ),
                                    "pcs_cost_attribution": openapi.Schema(
                                        type=openapi.TYPE_STRING
                                    ),
                                    "pcs_cost_relates_to": openapi.Schema(
                                        type=openapi.TYPE_STRING
                                    ),
                                    "pcs_cost_code": openapi.Schema(
                                        type=openapi.TYPE_STRING
                                    ),
                                    "pcs_cost_name": openapi.Schema(
                                        type=openapi.TYPE_STRING
                                    ),
                                    "pcs_rate": openapi.Schema(
                                        type=openapi.TYPE_NUMBER, nullable=True
                                    ),
                                    "pcs_frequency": openapi.Schema(
                                        type=openapi.TYPE_STRING
                                    ),
                                    "pcs_start_date": openapi.Schema(
                                        type=openapi.TYPE_STRING,
                                        format=openapi.FORMAT_DATE,
                                        nullable=True,
                                    ),
                                    "pcs_end_date": openapi.Schema(
                                        type=openapi.TYPE_STRING,
                                        format=openapi.FORMAT_DATE,
                                        nullable=True,
                                    ),
                                    "pcs_is_active": openapi.Schema(
                                        type=openapi.TYPE_BOOLEAN
                                    ),
                                    "pcs_status": openapi.Schema(
                                        type=openapi.TYPE_STRING
                                    ),
                                    "pcs_created_on": openapi.Schema(
                                        type=openapi.TYPE_STRING,
                                        format=openapi.FORMAT_DATETIME,
                                    ),
                                    "approver_details": openapi.Schema(
                                        type=openapi.TYPE_OBJECT, nullable=True
                                    ),
                                    "created_by_details": openapi.Schema(
                                        type=openapi.TYPE_OBJECT, nullable=True
                                    ),
                                    "deactivated_by_details": openapi.Schema(
                                        type=openapi.TYPE_OBJECT, nullable=True
                                    ),
                                },
                            ),
                        ),
                    },
                ),
            ),
            400: openapi.Response(description="Bad Request"),
            401: openapi.Response(description="Unauthorized"),
            403: openapi.Response(description="Forbidden"),
            500: openapi.Response(description="Internal Server Error"),
        },
        operation_id="list_recurring_cost_configs",
        operation_description="Retrieve list of Recurring Cost Configurations for a specific project company mapping",
    )
    @check_permissionV2("RC-SETUP", PermissionSetV2.VIEW)
    def list(self, request, *args, **kwargs):
        try:
            pcm_id = self.kwargs.get("pcm_id")
            queryset = ProjectCostSetup.objects.filter(
                pcm_id=pcm_id,
            ).order_by("pcs_created_on")
            serializer = ProjectCostSetupSerializer(queryset, many=True)
            return success_response(
                SUCCESS_MESSAGES["fetched"], serializer.data, status.HTTP_200_OK
            )
        except Exception as e:
            return error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @swagger_auto_schema(
        tags=["Recurring Cost"],
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Cost configuration updated successfully.",
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "pcs_id": openapi.Schema(type=openapi.TYPE_STRING),
                                "pcm_id": openapi.Schema(type=openapi.TYPE_STRING),
                                "prj_id": openapi.Schema(type=openapi.TYPE_STRING),
                                "pcs_cost_source": openapi.Schema(
                                    type=openapi.TYPE_STRING
                                ),
                                "pcs_cost_type": openapi.Schema(
                                    type=openapi.TYPE_STRING
                                ),
                                "pcs_cost_attribution": openapi.Schema(
                                    type=openapi.TYPE_STRING
                                ),
                                "pcs_cost_relates_to": openapi.Schema(
                                    type=openapi.TYPE_STRING
                                ),
                                "pcs_cost_code": openapi.Schema(
                                    type=openapi.TYPE_STRING
                                ),
                                "pcs_cost_name": openapi.Schema(
                                    type=openapi.TYPE_STRING
                                ),
                                "pcs_rate": openapi.Schema(
                                    type=openapi.TYPE_NUMBER, nullable=True
                                ),
                                "pcs_frequency": openapi.Schema(
                                    type=openapi.TYPE_STRING
                                ),
                                "pcs_start_date": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    format=openapi.FORMAT_DATE,
                                    nullable=True,
                                ),
                                "pcs_end_date": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    format=openapi.FORMAT_DATE,
                                    nullable=True,
                                ),
                                "pcs_is_active": openapi.Schema(
                                    type=openapi.TYPE_BOOLEAN
                                ),
                                "pcs_status": openapi.Schema(type=openapi.TYPE_STRING),
                                "pcs_created_on": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    format=openapi.FORMAT_DATETIME,
                                ),
                                "approver_details": openapi.Schema(
                                    type=openapi.TYPE_OBJECT, nullable=True
                                ),
                                "created_by_details": openapi.Schema(
                                    type=openapi.TYPE_OBJECT, nullable=True
                                ),
                                "deactivated_by_details": openapi.Schema(
                                    type=openapi.TYPE_OBJECT, nullable=True
                                ),
                            },
                        ),
                    },
                ),
            ),
            400: openapi.Response(description="Bad Request"),
            401: openapi.Response(description="Unauthorized"),
            403: openapi.Response(description="Forbidden"),
            404: openapi.Response(description="Not Found"),
            500: openapi.Response(description="Internal Server Error"),
        },
        operation_id="update_recurring_cost_config",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "pcs_cost_source": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    enum=["FIELD", "OFFICE", "ADMINISTRATIVE", "FINANCE"],
                    description="Source of the cost",
                ),
                "pcs_cost_type": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    enum=["FIX", "VAR"],
                    description="Fixed or variable cost",
                ),
                "pcs_cost_attribution": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    enum=["DIR", "IND"],
                    description="Direct or indirect cost attribution",
                ),
                "pcs_cost_relates_to": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    enum=["MAT", "EQU", "LAB", "SUB", "OT", "OH"],
                    description="What the cost relates to",
                ),
                "pcs_cost_code": openapi.Schema(
                    type=openapi.TYPE_STRING, description="Cost code"
                ),
                "pcs_cost_name": openapi.Schema(
                    type=openapi.TYPE_STRING, description="Name of the cost"
                ),
                "pcs_rate": openapi.Schema(
                    type=openapi.TYPE_NUMBER,
                    description="Rate for the cost",
                    nullable=True,
                ),
                "pcs_frequency": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    enum=[
                        "ADHOC",
                        "DAILY",
                        "WEEKLY",
                        "BIWEEKLY",
                        "MONTHLY",
                        "QUARTERLY",
                        "YEARLY",
                    ],
                    description="Frequency of the cost",
                ),
                "pcs_start_date": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    format=openapi.FORMAT_DATE,
                    description="Start date for recurring cost",
                    nullable=True,
                ),
                "pcs_end_date": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    format=openapi.FORMAT_DATE,
                    description="End date for recurring cost",
                    nullable=True,
                ),
                "pcs_vendor": openapi.Schema(
                    type=openapi.TYPE_STRING, description="Vendor name", nullable=True
                ),
                "pcs_approver": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Approver user ID",
                    nullable=True,
                ),
            },
        ),
        operation_description="Update an existing Recurring Cost Configuration",
    )
    @check_permissionV2("RC-SETUP", PermissionSetV2.UPDATE)
    def update(self, request, *args, **kwargs):
        try:
            pcs_id = self.kwargs.get("pcs_id")
            user_id = request.user.user_id
            data = request.data.copy()
            # Not allowing to update status
            if "pcs_status" in data:
                data.pop("pcs_status", None)

            instance = ProjectCostSetup.objects.get(pcs_id=pcs_id)
            update_field = data.get("update_field", "")
            old_value = ""
            new_value = ""
            if update_field:
                old_value = getattr(instance, update_field, None)
                new_value = data.get(update_field, None)
                if update_field == "pcs_approver":
                    old_value = get_full_name(old_value.user_id) if old_value else ""
                    new_value = get_full_name(new_value)
                update_field = activity_log_key_mapping_rc.get(update_field)
            serializer = ProjectCostSetupSerializer(instance, data=data, partial=True)
            if serializer.is_valid():
                serializer.save()
                user_name = get_full_name(user_id)
                pcm_id = getattr(instance.pcm_id, "pcm_id", "")
                event_payload = {
                    "pcm_id": pcm_id,
                    "obj_id": serializer.data.get("pcs_id"),
                    "obj_type": "projectcostsetup",
                    "user_id": user_id,
                    "syo_code": "RC-SETUP",
                    "request": request,
                    "to_users_pcm": [
                        {
                            "user_id": user_id,
                            "pcm_id": pcm_id,
                        }
                    ],
                    "log_action": activity_log_templates["rc_update"](
                        user_name, update_field, old_value, new_value
                    ),
                }
                EventService(
                    payload=event_payload,
                )
                return success_response(
                    SUCCESS_MESSAGES["updated"], serializer.data, status.HTTP_200_OK
                )

            # If serializer is not valid
            formatted_errors = format_serializer_errors(serializer.errors)
            return error_response(formatted_errors, status.HTTP_400_BAD_REQUEST)

        except ProjectCostSetup.DoesNotExist:
            return error_response(
                RECURRING_COST_ERRORS["not_found"], status.HTTP_404_NOT_FOUND
            )
        except ValidationError as ve:
            return error_response(str(ve), status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @swagger_auto_schema(
        tags=["Recurring Cost"],
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Cost configuration fetched successfully.",
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "pcs_id": openapi.Schema(type=openapi.TYPE_STRING),
                                "pcm_id": openapi.Schema(type=openapi.TYPE_STRING),
                                "prj_id": openapi.Schema(type=openapi.TYPE_STRING),
                                "pcs_cost_source": openapi.Schema(
                                    type=openapi.TYPE_STRING
                                ),
                                "pcs_cost_type": openapi.Schema(
                                    type=openapi.TYPE_STRING
                                ),
                                "pcs_cost_attribution": openapi.Schema(
                                    type=openapi.TYPE_STRING
                                ),
                                "pcs_cost_relates_to": openapi.Schema(
                                    type=openapi.TYPE_STRING
                                ),
                                "pcs_cost_code": openapi.Schema(
                                    type=openapi.TYPE_STRING
                                ),
                                "pcs_cost_name": openapi.Schema(
                                    type=openapi.TYPE_STRING
                                ),
                                "pcs_rate": openapi.Schema(
                                    type=openapi.TYPE_NUMBER, nullable=True
                                ),
                                "pcs_frequency": openapi.Schema(
                                    type=openapi.TYPE_STRING
                                ),
                                "pcs_start_date": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    format=openapi.FORMAT_DATE,
                                    nullable=True,
                                ),
                                "pcs_end_date": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    format=openapi.FORMAT_DATE,
                                    nullable=True,
                                ),
                                "pcs_is_active": openapi.Schema(
                                    type=openapi.TYPE_BOOLEAN
                                ),
                                "pcs_status": openapi.Schema(type=openapi.TYPE_STRING),
                                "pcs_created_on": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    format=openapi.FORMAT_DATETIME,
                                ),
                                "approver_details": openapi.Schema(
                                    type=openapi.TYPE_OBJECT, nullable=True
                                ),
                                "created_by_details": openapi.Schema(
                                    type=openapi.TYPE_OBJECT, nullable=True
                                ),
                                "deactivated_by_details": openapi.Schema(
                                    type=openapi.TYPE_OBJECT, nullable=True
                                ),
                            },
                        ),
                    },
                ),
            ),
            400: openapi.Response(description="Bad Request"),
            401: openapi.Response(description="Unauthorized"),
            403: openapi.Response(description="Forbidden"),
            404: openapi.Response(description="Not Found"),
            500: openapi.Response(description="Internal Server Error"),
        },
        operation_id="get_recurring_cost_config",
        operation_description="Retrieve details of a single Recurring Cost Configuration",
    )
    @check_permissionV2("RC-SETUP", PermissionSetV2.VIEW)
    def retrieve(self, request, *args, **kwargs):
        try:
            pcs_id = self.kwargs.get("pcs_id")
            queryset = ProjectCostSetup.objects.get(pcs_id=pcs_id)
            serializer = ProjectCostSetupSerializer(queryset)
            return success_response(
                SUCCESS_MESSAGES["fetched"], serializer.data, status.HTTP_200_OK
            )

        except ProjectCostSetup.DoesNotExist:
            return error_response(
                RECURRING_COST_ERRORS["not_found"], status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @swagger_auto_schema(
        tags=["Recurring Cost"],
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Cost configuration deleted successfully.",
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "pcs_id": openapi.Schema(type=openapi.TYPE_STRING),
                                "pcm_id": openapi.Schema(type=openapi.TYPE_STRING),
                                "pcs_status": openapi.Schema(
                                    type=openapi.TYPE_STRING, example="DELETED"
                                ),
                            },
                        ),
                    },
                ),
            ),
            400: openapi.Response(
                description="Bad Request",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="error"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Cannot delete cost configuration with associated records.",
                        ),
                    },
                ),
            ),
            401: openapi.Response(description="Unauthorized"),
            403: openapi.Response(description="Forbidden"),
            404: openapi.Response(description="Not Found"),
            500: openapi.Response(description="Internal Server Error"),
        },
        operation_id="delete_recurring_cost_config",
        operation_description="Delete (soft delete) a Recurring Cost Configuration by changing its status to DELETED",
    )
    @check_permissionV2("RC-SETUP", PermissionSetV2.DELETE)
    def destroy(self, request, *args, **kwargs):
        try:
            pcs_id = self.kwargs.get("pcs_id")
            user_id = request.user.user_id
            instance = ProjectCostSetup.objects.get(pcs_id=pcs_id)
            if instance.pcs_status != ProjectCostSetup.StatusChoices.OPEN:
                return error_response(
                    RECURRING_COST_ERRORS["invalid_delete"], status.HTTP_400_BAD_REQUEST
                )
            instance.pcs_status = ProjectCostSetup.StatusChoices.DELETED
            instance.save()
            serializer = ProjectCostSetupSerializer(instance)
            user_name = get_full_name(user_id)
            pcm_id = getattr(instance.pcm_id, "pcm_id", "")
            event_payload = {
                "pcm_id": pcm_id,
                "obj_id": serializer.data.get("pcs_id"),
                "obj_type": "projectcostsetup",
                "user_id": user_id,
                "syo_code": "RC-SETUP",
                "request": request,
                "to_users_pcm": [
                    {
                        "user_id": user_id,
                        "pcm_id": pcm_id,
                    }
                ],
                "log_action": activity_log_templates["rc_delete"](user_name),
            }
            EventService(
                payload=event_payload,
            )
            return success_response(
                SUCCESS_MESSAGES["deleted"],
                serializer.data,
                status.HTTP_200_OK,
            )
        except ProjectCostSetup.DoesNotExist:
            return error_response(
                RECURRING_COST_ERRORS["not_found"], status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @swagger_auto_schema(
        tags=["Recurring Cost"],
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["pcs_ids"],
            properties={
                "pcs_ids": openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_STRING),
                    description="List of recurring cost configuration IDs to approve",
                )
            },
        ),
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Cost configuration updated successfully.",
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_ARRAY,
                            items=openapi.Schema(
                                type=openapi.TYPE_OBJECT,
                                properties={
                                    "pcs_id": openapi.Schema(type=openapi.TYPE_STRING),
                                    "pcs_status": openapi.Schema(
                                        type=openapi.TYPE_STRING, example="ACCEPTED"
                                    ),
                                    "pcs_is_active": openapi.Schema(
                                        type=openapi.TYPE_BOOLEAN, example=True
                                    ),
                                    "pcs_active_from": openapi.Schema(
                                        type=openapi.TYPE_STRING,
                                        format=openapi.FORMAT_DATETIME,
                                    ),
                                },
                            ),
                        ),
                    },
                ),
            ),
            400: openapi.Response(
                description="Bad Request",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="error"
                        ),
                        "message": openapi.Schema(type=openapi.TYPE_STRING),
                    },
                ),
            ),
            401: openapi.Response(description="Unauthorized"),
            403: openapi.Response(description="Forbidden"),
            404: openapi.Response(description="Not Found"),
            500: openapi.Response(description="Internal Server Error"),
        },
        operation_id="approve_recurring_cost_configs",
        operation_description="Approve multiple Recurring Cost Configurations by changing their status to ACCEPTED",
    )
    @check_permissionV2("RC-SETUP", PermissionSetV2.ACCEPT)
    def approve(self, request, *args, **kwargs):
        try:
            pcs_ids = request.data.get("pcs_ids", [])
            if not isinstance(pcs_ids, list):
                return error_response(
                    "Invalid request format. 'config ids' should be a list.",
                    status.HTTP_400_BAD_REQUEST,
                )

            if not pcs_ids:
                return error_response(
                    "No valid config ids provided.",
                    status.HTTP_400_BAD_REQUEST,
                )

            configs = ProjectCostSetup.objects.filter(pcs_id__in=pcs_ids)
            if not configs.exists():
                return error_response(
                    RECURRING_COST_ERRORS["not_found"], status.HTTP_404_NOT_FOUND
                )
            now = timezone.now()
            updated_instances = []
            for config in configs:
                if config.pcs_status not in [
                    ProjectCostSetup.StatusChoices.OPEN,
                    ProjectCostSetup.StatusChoices.REVISE,
                ]:
                    return error_response(
                        RECURRING_COST_ERRORS["invalid_approve"],
                        status.HTTP_400_BAD_REQUEST,
                    )
                config.pcs_status = ProjectCostSetup.StatusChoices.ACCEPTED
                config.pcs_is_active = True
                config.pcs_active_from = now
                updated_instances.append(config)

                serializer = ProjectCostSetupSerializer(data=config, partial=True)
                if serializer.is_valid():
                    updated_instances.append(config)

            if len(updated_instances) == 0 or not updated_instances:
                return error_response(
                    "No valid configurations to approve.",
                    status.HTTP_400_BAD_REQUEST,
                )
            ProjectCostSetup.objects.bulk_update(
                updated_instances, ["pcs_status", "pcs_active_from", "pcs_is_active"]
            )
            serializer = ProjectCostSetupSerializer(updated_instances, many=True)
            return success_response(
                SUCCESS_MESSAGES["updated"], serializer.data, status.HTTP_200_OK
            )
        except ProjectCostSetup.DoesNotExist:
            return error_response(
                RECURRING_COST_ERRORS["not_found"], status.HTTP_404_NOT_FOUND
            )
        except ValidationError as ve:
            return error_response(str(ve), status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @swagger_auto_schema(
        tags=["Recurring Cost"],
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["action"],
            properties={
                "action": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    enum=["activate", "deactivate"],
                    description="Action to perform on the configuration",
                ),
                "pcs_deactivation_reason": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Reason for deactivation (required when action is 'deactivate')",
                    nullable=True,
                ),
            },
        ),
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Cost Configuration deactivated successfully.",
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "pcs_id": openapi.Schema(type=openapi.TYPE_STRING),
                                "pcs_is_active": openapi.Schema(
                                    type=openapi.TYPE_BOOLEAN
                                ),
                                "pcs_deactivated_on": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    format=openapi.FORMAT_DATETIME,
                                    nullable=True,
                                ),
                                "pcs_deactivation_reason": openapi.Schema(
                                    type=openapi.TYPE_STRING, nullable=True
                                ),
                                "deactivated_by_details": openapi.Schema(
                                    type=openapi.TYPE_OBJECT,
                                    properties={
                                        "user_id": openapi.Schema(
                                            type=openapi.TYPE_STRING
                                        ),
                                        "name": openapi.Schema(
                                            type=openapi.TYPE_STRING
                                        ),
                                    },
                                    nullable=True,
                                ),
                            },
                        ),
                    },
                ),
            ),
            400: openapi.Response(
                description="Bad Request",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="error"
                        ),
                        "message": openapi.Schema(type=openapi.TYPE_STRING),
                    },
                ),
            ),
            401: openapi.Response(description="Unauthorized"),
            403: openapi.Response(description="Forbidden"),
            404: openapi.Response(description="Not Found"),
            500: openapi.Response(description="Internal Server Error"),
        },
        operation_id="toggle_recurring_cost_config_activation",
        operation_description="Activate or deactivate a Recurring Cost Configuration",
    )
    @check_permissionV2("RC-SETUP", PermissionSetV2.DISABLE)
    def activation(self, request, *args, **kwargs):
        try:
            pcs_id = self.kwargs.get("pcs_id")
            user_id = request.user.user_id
            data = request.data.copy()
            instance = ProjectCostSetup.objects.get(pcs_id=pcs_id)
            if instance.pcs_status in [
                ProjectCostSetup.StatusChoices.CLOSED,
                ProjectCostSetup.StatusChoices.DELETED,
            ]:
                return error_response(
                    RECURRING_COST_ERRORS["already_deactivated"],
                    status.HTTP_400_BAD_REQUEST,
                )
            if instance.pcs_status not in [ProjectCostSetup.StatusChoices.ACCEPTED]:
                return error_response(
                    RECURRING_COST_ERRORS["pending_deactivate"],
                    status.HTTP_400_BAD_REQUEST,
                )
            action_str = data.get("action", "").lower()
            log_action = "Cost configuration updated"
            user_name = get_full_name(user_id)
            if action_str == "activate":
                instance.pcs_is_active = True
                instance.pcs_deactivated_on = None
                instance.pcs_deactivation_reason = None
                log_action = activity_log_templates["rc_reactivate"](user_name)
            elif action_str == "deactivate":
                instance.pcs_is_active = False
                instance.pcs_deactivation_reason = data.get(
                    "pcs_deactivation_reason", ""
                )
                instance.pcs_deactivated_on = timezone.now()
                log_action = activity_log_templates["rc_deactivate"](user_name)
            else:
                return error_response(
                    "Invalid action. Must be 'activate' or 'deactivate'.",
                    status.HTTP_400_BAD_REQUEST,
                )

            user = company_employee.objects.get(user_id=user_id)
            instance.pcs_deactivated_by = user

            instance.save()
            serializer = ProjectCostSetupSerializer(instance)
            pcm_id = getattr(instance.pcm_id, "pcm_id", "")
            event_payload = {
                "pcm_id": pcm_id,
                "obj_id": serializer.data.get("pcs_id"),
                "obj_type": "projectcostsetup",
                "user_id": user_id,
                "syo_code": "RC-SETUP",
                "request": request,
                "to_users_pcm": [
                    {
                        "user_id": user_id,
                        "pcm_id": pcm_id,
                    }
                ],
                "log_action": log_action,
            }
            EventService(
                payload=event_payload,
            )
            return success_response(
                SUCCESS_MESSAGES["deactivated"], serializer.data, status.HTTP_200_OK
            )
        except company_employee.DoesNotExist:
            return error_response(
                RECURRING_COST_ERRORS["user_not_found"], status.HTTP_404_NOT_FOUND
            )
        except ProjectCostSetup.DoesNotExist:
            return error_response(
                RECURRING_COST_ERRORS["not_found"], status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @swagger_auto_schema(
        tags=["Recurring Cost"],
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING, example="Approver List"
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_ARRAY,
                            items=openapi.Schema(
                                type=openapi.TYPE_OBJECT,
                                properties={
                                    "user_id": openapi.Schema(type=openapi.TYPE_STRING),
                                    "full_name": openapi.Schema(
                                        type=openapi.TYPE_STRING
                                    ),
                                    "email": openapi.Schema(
                                        type=openapi.TYPE_STRING,
                                        format=openapi.FORMAT_EMAIL,
                                    ),
                                    "role": openapi.Schema(type=openapi.TYPE_STRING),
                                },
                            ),
                        ),
                    },
                ),
            ),
            400: openapi.Response(description="Bad Request"),
            401: openapi.Response(description="Unauthorized"),
            403: openapi.Response(description="Forbidden"),
            500: openapi.Response(description="Internal Server Error"),
        },
        operation_id="get_approver_reviewer_list",
        operation_description="Get list of users who can approve or review recurring cost configurations",
    )
    def get_approver_reviewer_list(self, request, *args, **kwargs):
        pcm_id = self.kwargs.get("pcm_id")
        try:
            result = get_user_having_permission(pcm_id, "RC-COST", "LIN-APPROVE")

            return success_response(
                SUCCESS_MESSAGES.get("approver_list", "Approver List"),
                result,
                status.HTTP_200_OK,
            )
        except Exception as e:
            return error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @swagger_auto_schema(
        tags=["Recurring Cost"],
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING, example="Fetched successfully"
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_ARRAY,
                            items=openapi.Schema(
                                type=openapi.TYPE_OBJECT,
                                properties={
                                    "pcs_id": openapi.Schema(type=openapi.TYPE_STRING),
                                    "pcs_cost_code": openapi.Schema(
                                        type=openapi.TYPE_STRING
                                    ),
                                    "pcs_cost_name": openapi.Schema(
                                        type=openapi.TYPE_STRING
                                    ),
                                    "pcs_cost_relates_to": openapi.Schema(
                                        type=openapi.TYPE_STRING
                                    ),
                                    "pcs_created_on": openapi.Schema(
                                        type=openapi.TYPE_STRING,
                                        format=openapi.FORMAT_DATETIME,
                                    ),
                                    "pcs_approver__emp_first_name": openapi.Schema(
                                        type=openapi.TYPE_STRING, nullable=True
                                    ),
                                    "pcs_approver__emp_last_name": openapi.Schema(
                                        type=openapi.TYPE_STRING, nullable=True
                                    ),
                                    "pcs_created_by__emp_first_name": openapi.Schema(
                                        type=openapi.TYPE_STRING
                                    ),
                                    "pcs_created_by__emp_last_name": openapi.Schema(
                                        type=openapi.TYPE_STRING
                                    ),
                                },
                            ),
                        ),
                    },
                ),
            ),
            400: openapi.Response(description="Bad Request"),
            401: openapi.Response(description="Unauthorized"),
            403: openapi.Response(description="Forbidden"),
            500: openapi.Response(description="Internal Server Error"),
        },
        operation_id="list_adhoc_frequency_costs",
        operation_description="List all ADHOC frequency costs with their details for a specific project company mapping",
    )
    def list_adhoc_costs(self, request, *args, **kwargs):
        try:
            pcm_id = self.kwargs.get("pcm_id")
            queryset = (
                ProjectCostSetup.objects.filter(
                    pcm_id=pcm_id,
                    pcs_frequency=ProjectCostSetup.FrequencyChoices.ADHOC,
                    pcs_status=ProjectCostSetup.StatusChoices.ACCEPTED,
                )
                .values(
                    "pcs_id",
                    "pcs_cost_code",
                    "pcs_cost_name",
                    "pcs_cost_relates_to",
                    "pcs_created_on",
                    "pcs_approver__emp_first_name",
                    "pcs_approver__emp_last_name",
                    "pcs_created_by__emp_first_name",
                    "pcs_created_by__emp_last_name",
                )
                .order_by("pcs_created_on")
            )
            return success_response(
                SUCCESS_MESSAGES.get("fetched", "Fetched successfully"),
                list(queryset),
                status.HTTP_200_OK,
            )
        except Exception as e:
            return error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
