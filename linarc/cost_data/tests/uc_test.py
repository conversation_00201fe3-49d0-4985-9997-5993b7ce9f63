from rest_framework.test import APITestCase, APIClient
from cost_data.models import ProjectCostSetup, UnapprovedCost
from .data_factory import (
    create_company,
    create_user,
    create_employee,
    create_project,
    create_project_company,
    get_basic_cost_setup_data,
    get_adhoc_cost_data,
    create_employee_signature,
    create_fake_unapproved_costs,
    create_company_role,
    create_company_employee_roles,
    add_rbac,
)
from django.utils import timezone


class ConfigTest(APITestCase):
    @classmethod
    def setUpTestData(cls):
        cls.company = create_company()
        cls.roles = create_company_role(cls.company)
        pm_role = cls.roles[0]
        fm_role = cls.roles[1]
        cls.login_user = create_user("testuser", "testpass")
        cls.employee = create_employee(cls.login_user, cls.company, pm_role)
        cls.employee_role = create_company_employee_roles(
            cls.employee, cls.company, pm_role
        )
        cls.project = create_project(cls.employee)
        cls.project_company = create_project_company(
            cls.project, cls.employee, cls.company
        )
        cls.employee_signature = create_employee_signature(cls.employee)
        add_rbac()

    def setUp(self):
        self.client = APIClient()
        response = self.client.post(
            "/api-token-auth/",
            {"username": "testuser", "password": "testpass"},
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        self.token = response.data.get("access")
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {self.token}")

    def test_addhoc_unapproved_cost(self):
        pcs = get_basic_cost_setup_data(
            self.project_company, self.project, self.employee
        )
        pcs["pcs_frequency"] = ProjectCostSetup.FrequencyChoices.ADHOC
        pcs["pcs_start_date"] = None
        pcs["pcs_end_date"] = None
        pcs["pcs_rate"] = None
        pcs["pcm_id"] = self.project_company
        pcs["pcs_approver"] = self.employee
        pcs["prj_id"] = self.project
        pcs["pcs_created_by"] = self.employee

        response = ProjectCostSetup.objects.create(**pcs)
        # pcs_instance = response.data.get("data", {})

        uac_payload = get_adhoc_cost_data()

        response = self.client.post(
            f"/api/v1/cost_data/recurring_cost/{self.project_company.pcm_id}/bulk/upload/",
            uac_payload,
            format="json",
        )
        uac_result = response.data.get("data", {})

        self.assertEqual(response.status_code, 201)
        self.assertEqual(uac_result["total_qty"], uac_payload["qty"])

        # update ua cost with configurations

    def test_update_uploaded_adhoc_cost(self):
        pcs = get_basic_cost_setup_data(
            self.project_company, self.project, self.employee
        )
        pcs["pcs_frequency"] = ProjectCostSetup.FrequencyChoices.ADHOC
        pcs["pcs_start_date"] = None
        pcs["pcs_end_date"] = None
        pcs["pcs_rate"] = None

        pcs["pcm_id"] = self.project_company
        pcs["pcs_approver"] = self.employee
        pcs["prj_id"] = self.project
        pcs["pcs_created_by"] = self.employee

        pcs_instance = ProjectCostSetup.objects.create(**pcs)
        uac_payload = get_adhoc_cost_data()

        response = self.client.post(
            f"/api/v1/cost_data/recurring_cost/{self.project_company.pcm_id}/bulk/upload/",
            uac_payload,
            format="json",
        )
        uac_result = response.data.get("data", {})
        self.assertEqual(response.status_code, 201)
        self.assertEqual(uac_result["total_qty"], uac_payload["qty"])

        update_uac_payload = {
            "pcs_id": pcs_instance.pcs_id,
            "uac_cost_code": pcs_instance.pcs_cost_code,
            "uac_cost_name": pcs_instance.pcs_cost_name,
        }
        uac_id = uac_result.get("uac_id", None)
        response = self.client.patch(
            f"/api/v1/cost_data/recurring_cost/{uac_id}/cost/",
            update_uac_payload,
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        updated_uac = response.data.get("data", {})
        self.assertEqual(updated_uac["cost_setup_id"], pcs_instance.pcs_id)
        self.assertEqual(updated_uac["uac_cost_code"], pcs_instance.pcs_cost_code)

    def test_addhoc_unapproved_cost_missing_fields(self):
        incomplete_data = {}
        response = self.client.post(
            f"/api/v1/cost_data/recurring_cost/{self.project_company.pcm_id}/bulk/upload/",
            incomplete_data,
            format="json",
        )
        self.assertEqual(response.status_code, 400)
        self.assertIn("No cost entries provided", response.data["message"])

    def test_update_approved_cost(self):
        pcs_payload = get_basic_cost_setup_data(
            self.project_company, self.project, self.employee
        )
        pcs_payload["pcm_id"] = self.project_company
        pcs_payload["pcs_approver"] = self.employee
        pcs_payload["prj_id"] = self.project
        pcs_payload["pcs_created_by"] = self.employee
        pcs_payload["pcs_status"] = ProjectCostSetup.StatusChoices.ACCEPTED
        pcs_payload["pcs_is_active"] = True

        pcs_instance = ProjectCostSetup.objects.create(**pcs_payload)

        uac_payload = {
            "uac_cost_code": pcs_instance.pcs_cost_code,
            "uac_cost_name": pcs_instance.pcs_cost_name,
            "uac_actual_cost": pcs_instance.pcs_rate,
            "uac_added_on": timezone.now(),
            "uac_qty": 1,
            "uac_vendor": pcs_instance.pcs_vendor,
            "cost_setup_id": pcs_instance,
            "uac_meta_data": {
                "pcs_cost_attribution": pcs_instance.pcs_cost_attribution,
                "pcs_cost_type": pcs_instance.pcs_cost_type,
                "pcs_cost_source": pcs_instance.pcs_cost_source,
            },
            "pcm_id": pcs_instance.pcm_id,
            "uac_status": UnapprovedCost.StatusChoices.APPROVED,
        }
        uac_instance = UnapprovedCost.objects.create(**uac_payload)

        uac_payload = {
            "uac_actual_cost": 2000.00,
            "uac_vendor": "John Doe",
        }
        response = self.client.patch(
            f"/api/v1/cost_data/recurring_cost/{uac_instance.uac_id}/cost/",
            uac_payload,
            format="json",
        )
        self.assertEqual(response.status_code, 400)
        self.assertIn("already approved", response.data["message"])

    def test_approve_unapproved_costs(self):
        pcs_payload = get_basic_cost_setup_data(
            self.project_company, self.project, self.employee
        )
        pcs_payload["pcm_id"] = self.project_company
        pcs_payload["pcs_approver"] = self.employee
        pcs_payload["prj_id"] = self.project
        pcs_payload["pcs_created_by"] = self.employee
        pcs_payload["pcs_status"] = ProjectCostSetup.StatusChoices.ACCEPTED
        pcs_payload["pcs_is_active"] = True

        pcs_instance = ProjectCostSetup.objects.create(**pcs_payload)

        uacs_items = create_fake_unapproved_costs(
            pcs_instance, self.project_company, count=5
        )
        cost_list = UnapprovedCost.objects.bulk_create(uacs_items)
        uac_ids = [str(uac.uac_id) for uac in cost_list]
        data = {"uac_ids": uac_ids}
        response = self.client.patch(
            "/api/v1/cost_data/recurring_cost/approve/all/", data, format="json"
        )
        result = response.data.get("data", {})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(result["approved_count"], len(uac_ids))

    def test_delete_unapproved_cost(self):
        pcs_payload = get_basic_cost_setup_data(
            self.project_company, self.project, self.employee
        )
        pcs_payload["pcm_id"] = self.project_company
        pcs_payload["pcs_approver"] = self.employee
        pcs_payload["prj_id"] = self.project
        pcs_payload["pcs_created_by"] = self.employee
        pcs_payload["pcs_status"] = ProjectCostSetup.StatusChoices.ACCEPTED
        pcs_payload["pcs_is_active"] = True

        pcs_instance = ProjectCostSetup.objects.create(**pcs_payload)

        uac_payload = {
            "uac_cost_code": pcs_instance.pcs_cost_code,
            "uac_cost_name": pcs_instance.pcs_cost_name,
            "uac_actual_cost": pcs_instance.pcs_rate,
            "uac_added_on": timezone.now(),
            "uac_qty": 1,
            "uac_vendor": pcs_instance.pcs_vendor,
            "cost_setup_id": pcs_instance,
            "uac_meta_data": {
                "pcs_cost_attribution": pcs_instance.pcs_cost_attribution,
                "pcs_cost_type": pcs_instance.pcs_cost_type,
                "pcs_cost_source": pcs_instance.pcs_cost_source,
            },
            "pcm_id": pcs_instance.pcm_id,
            "uac_status": UnapprovedCost.StatusChoices.PENDING,
        }
        uac_instance = UnapprovedCost.objects.create(**uac_payload)
        payload = {"uac_ids": [str(uac_instance.uac_id)]}
        response = self.client.delete(
            f"/api/v1/cost_data/recurring_cost/delete/many/",
            payload,
            format="json",
        )
        result = response.data.get("data", {})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(result["deleted_count"], 1)
