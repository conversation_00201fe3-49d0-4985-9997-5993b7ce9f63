from random import choice
from faker import Faker
from projects.models import Project, Project_Company as ProjectCompany
from companies.models import company_employee, CompanyRole
from companies.models import company_task_codes, company, company_employee_roles
from cost_data.models import UnapprovedCost, ProjectCostSetup
from django.utils import timezone
from system.models import Building_Type, Building_Sub_Type, Contractor_Business_Type
from companies.models import login_user as LoginUser
from shared.models.employee_signature import EmployeeSignature
import base64

fake = Faker()
import datetime
from rbac.models import (
    PermissionStore,
    SystemObjects,
    PermissionForObjects,
    SystemObjectPermission,
    CompanyObjects,
    CompanyRolePermission,
)
from system.models import SystemRoles, SystemApp


def create_company():
    return company.objects.create(
        cmp_name="Stark Industries",
        cmp_website="https://stark.com",
        cmp_status=company.status_choices.ACTIVE,
        cmp_is_onboarded=True,
        cmp_onboard_stage=company.OnboardingStage.COMPANY_INFO,
    )


def create_user(username, password):
    return LoginUser.objects.create_user(
        user_id=username, username=username, password=password
    )


roles = [
    {
        "rol_type": CompanyRole.roltype_choices.PROJECT_MANAGER,
        "rol_name": "Project Manager",
        "rol_code": "PM",
        "rol_url": "PM",
    },
    {
        "rol_type": CompanyRole.roltype_choices.FOREMAN,
        "rol_name": "Foreman",
        "rol_code": "FM",
        "rol_url": "FM",
    },
]


def create_company_role(company):
    roles_instances = []

    for role in roles:
        # Get or create SystemApp entry
        system_app, _ = SystemApp.objects.get_or_create(
            sys_app_id=role["rol_url"],
            defaults={
                "sys_app_name": role["rol_url"],
                "sys_app_device": SystemApp.device_choices.WEBAPP,  # or MOBILE/BOTH if you prefer
            },
        )

        # Build CompanyRole instance
        role_obj = CompanyRole(
            cmp_id=company,
            rol_type=role["rol_type"],
            rol_name=role["rol_name"],
            rol_code=role["rol_code"],
            rol_url=system_app,
        )
        roles_instances.append(role_obj)

    # Bulk create roles (skip duplicates if already created)
    created_roles = CompanyRole.objects.bulk_create(
        roles_instances, ignore_conflicts=True
    )
    return created_roles


def create_employee(user, company, role):
    return company_employee.objects.create(
        user_id=user.user_id,
        cmp_id=company,
        emp_first_name="Tony",
        emp_last_name="Stark",
        emp_email="<EMAIL>",
        emp_phone="1234567890",
        emp_status=company_employee.status_choices.ACTIVE,
        role_id_main=role,
    )


def create_company_employee_roles(employee, company, role):
    try:
        company_employee_roles.objects.get(user_id=employee.user_id, cmp_id=company)
    except company_employee_roles.DoesNotExist:
        return company_employee_roles.objects.create(
            user_id=employee,
            cmp_id=company,
            rol_id=role,
        )
    else:
        print(
            f"Employee {employee.emp_first_name} already has a role in {company.cmp_name}"
        )


def create_project(employee):
    building_type = Building_Type.objects.create(blt_type="IN", blt_name="Industrial")
    # Create Building Sub Type
    building_sub_type = Building_Sub_Type.objects.create(
        bls_type="IN1", blt_type=building_type.blt_type, bls_name="Factory"
    )
    contractor_business_type = Contractor_Business_Type.objects.create(
        cbt_code="GC", cbt_name="Installer"
    )
    return Project.objects.create(
        prj_name="Iron Man Suit",
        prj_status="AC",
        prj_sched_type="GC",
        prj_schedule_mode="ST",
        prj_stage="DS",
        prj_start_date=datetime.date.today(),
        prj_end_date=datetime.date.today() + datetime.timedelta(days=30),
        prj_createdon=datetime.date.today(),
        prj_desc="Secret project by Stark",
        cnt_code="US",
        project_type="IN",
        my_role=contractor_business_type,
        prj_createdby=employee,
        blt_type=building_type,
        bls_type=building_sub_type,
    )


def create_project_company(project, employee, company):
    return ProjectCompany.objects.create(
        prj_id=project,
        pcm_invitedby=employee,
        cmp_id=company,
        pcm_cbt_code="GC",
        pcm_cst_code="GC",
        pcm_status=ProjectCompany.status_choices.ACTIVE,
        pcm_start_date=timezone.now().date(),
        pcm_invitedon=timezone.now().date(),
    )


def get_basic_cost_setup_data(project_company, project, employee):
    return {
        "pcm_id": project_company.pcm_id,
        "prj_id": project.prj_id,
        "pcs_frequency": ProjectCostSetup.FrequencyChoices.DAILY,
        "pcs_start_date": timezone.now().date(),
        "pcs_end_date": timezone.now().date() + datetime.timedelta(days=30),
        "pcs_cost_source": ProjectCostSetup.CostSourceChoices.FIELD,
        "pcs_cost_code": "3289.23",
        "pcs_cost_name": "Cost for Materials",
        "pcs_cost_type": ProjectCostSetup.CostTypeChoices.FIXED,
        "pcs_cost_relates_to": ProjectCostSetup.CostRelatesToChoices.MATERIAL,
        "pcs_cost_attribution": ProjectCostSetup.CostAttributionChoices.DIRECT,
        "pcs_rate": 1000.00,
        "pcs_approver": employee.user_id,
        "pcs_vendor": "John Doe Enterprises",
    }


def get_adhoc_cost_data():
    cost_data = {
        "total_cost": 5000,
        "vendor": "FF",
        "qty": 5,
        "receipt_no": "2189KJKJD7",
        "cost_entries": [
            {
                "name": "Cement",
                "uom": "KG",
                "unit_rate": 20,
                "qty": 2,
                "descriptions": "hs",
            },
            {
                "name": "Sand",
                "uom": "CFT",
                "unit_rate": 15,
                "qty": 10,
                "descriptions": "hs",
            },
            {
                "name": "Bricks",
                "uom": "PCS",
                "unit_rate": 8,
                "qty": 100,
                "descriptions": "hs",
            },
            {
                "name": "Steel Rod",
                "uom": "KG",
                "unit_rate": 70,
                "qty": 5,
                "descriptions": "hs",
            },
            {
                "name": "Paint",
                "uom": "LTR",
                "unit_rate": 120,
                "qty": 3,
                "descriptions": "hs",
            },
        ],
    }
    return cost_data


def create_employee_signature(employee):
    sample_signature_str = "Sample Signature Content"
    signature_base64 = base64.b64encode(sample_signature_str.encode("utf-8")).decode(
        "utf-8"
    )

    # Create instance without setting the signature yet
    employee_signature = EmployeeSignature(
        employee=employee,
        type="DRAW",
        is_active=True,
        created_by=employee,
        employee_name=None,
        employee_mobile_number=None,
    )

    # Properly decode base64 string into binary
    employee_signature.set_signature(signature_base64)

    # Save to DB
    employee_signature.save()
    return employee_signature


def create_fake_unapproved_costs(pcs_instance, project_company, count=5):
    """
    Create a list of UnapprovedCost model instances.
    """
    fake_costs = []
    for _ in range(count):
        cost = UnapprovedCost(
            uac_cost_code=pcs_instance.pcs_cost_code,
            uac_cost_name=fake.word().title(),
            uac_actual_cost=round(
                fake.pyfloat(left_digits=4, right_digits=2, positive=True), 2
            ),
            uac_added_on=timezone.now(),
            uac_qty=fake.random_int(min=1, max=10),
            uac_vendor=fake.company(),
            cost_setup_id=pcs_instance,
            uac_meta_data={
                "pcs_cost_attribution": pcs_instance.pcs_cost_attribution,
                "pcs_cost_type": pcs_instance.pcs_cost_type,
                "pcs_cost_source": pcs_instance.pcs_cost_source,
            },
            pcm_id=project_company,
            uac_status=UnapprovedCost.StatusChoices.PENDING,
        )
        fake_costs.append(cost)
    return fake_costs


permissions = {
    "LIN-VIEW": "View",
    "LIN-UPDATE": "Update",
    "LIN-DELETE": "Delete",
    "LIN-APPROVE": "Approve",
    "LIN-DISABLE": "Disable",
    "LIN-CREATE": "Create",
}

system_objects = [
    {
        "syo_code": "RC-SETUP",
        "syo_name": "Cost Configuration",
        "syo_relates_to": "RC",
        "syo_parent_name": "Recurring Cost",
        "syo_sort_no": "750",
        "syo_app": ["PM"],
    },
    {
        "syo_code": "RC-COST",
        "syo_name": "Unapproved Costs",
        "syo_relates_to": "RC",
        "syo_parent_name": "Recurring Cost",
        "syo_sort_no": "751",
        "syo_app": ["PM"],
    },
]

objects_permissions = {
    "RC-SETUP": [
        "LIN-VIEW",
        "LIN-CREATE",
        "LIN-DELETE",
        "LIN-APPROVE",
        "LIN-DISABLE",
        "LIN-UPDATE",
    ],
    "RC-COST": ["LIN-VIEW", "LIN-CREATE", "LIN-DELETE", "LIN-APPROVE", "LIN-UPDATE"],
}

system_object_permission = {
    "GC": [
        {
            "syo_code": "RC-SETUP",
            "permissions": [
                "LIN-VIEW",
                "LIN-CREATE",
                "LIN-DELETE",
                "LIN-APPROVE",
                "LIN-DISABLE",
                "LIN-UPDATE",
            ],
        },
        {
            "syo_code": "RC-COST",
            "permissions": [
                "LIN-VIEW",
                "LIN-CREATE",
                "LIN-DELETE",
                "LIN-APPROVE",
                "LIN-UPDATE",
            ],
        },
    ]
}


system_role_permissions = {
    CompanyRole.roltype_choices.PROJECT_MANAGER: {
        "RC-COST": [
            "LIN-VIEW",
            "LIN-CREATE",
            "LIN-DELETE",
            "LIN-APPROVE",
            "LIN-DISABLE",
            "LIN-UPDATE",
        ],
        "RC-SETUP": [
            "LIN-VIEW",
            "LIN-CREATE",
            "LIN-DELETE",
            "LIN-APPROVE",
            "LIN-DISABLE",
            "LIN-UPDATE",
        ],
    },
    CompanyRole.roltype_choices.FOREMAN: {
        "RC-COST": [
            "LIN-VIEW",
            "LIN-CREATE",
        ]
    },
}


def create_permission_store(permissions):
    for perm_id, perm_name in permissions.items():
        try:
            PermissionStore.objects.get(perm_id=perm_id)
        except PermissionStore.DoesNotExist:
            PermissionStore.objects.create(perm_id=perm_id, perm_name=perm_name)
    return PermissionStore.objects.all()


def create_syo_objects(syo_object_list):
    for obj in syo_object_list:
        syo_code = obj.get("syo_code")
        syo_name = obj.get("syo_name")
        syo_relates_to = obj.get("syo_relates_to")
        syo_parent_name = obj.get("syo_parent_name")
        syo_sort_no = obj.get("syo_sort_no")
        syo_app_codes = obj.get("syo_app", [])

        # Check if object already exists
        system_object, created = SystemObjects.objects.get_or_create(
            syo_code=syo_code,
            defaults={
                "syo_name": syo_name,
                "syo_relates_to": syo_relates_to,
                "syo_parent_name": syo_parent_name,
                "syo_sort_no": syo_sort_no,
            },
        )
        # Set ManyToMany relation (must be done after save)
        apps = SystemApp.objects.filter(sys_app_id__in=syo_app_codes)
        system_object.syo_app.set(apps)  # overwrites any existing relations
        system_object.save()
    return SystemObjects.objects.all()


def add_permissions_to_objects(permissions, objects_permissions):
    # Step 1: Ensure all permissions exist
    for perm_id, perm_name in permissions.items():
        PermissionStore.objects.get_or_create(
            perm_id=perm_id, defaults={"perm_name": perm_name}
        )

    # Step 2: Link permissions to system objects
    for syo_code, perm_ids in objects_permissions.items():
        try:
            system_object = SystemObjects.objects.get(syo_code=syo_code)
        except SystemObjects.DoesNotExist:
            continue

        for perm_id in perm_ids:
            try:
                permission = PermissionStore.objects.get(perm_id=perm_id)
            except PermissionStore.DoesNotExist:
                continue

            # Create the mapping only if it doesn't exist
            _, created = PermissionForObjects.objects.get_or_create(
                syo_code=system_object,
                perm_id=permission,
            )


def create_system_object_permissions(system_object_permission):
    for cbt_code, syo_list in system_object_permission.items():
        try:
            cbt_instance = Contractor_Business_Type.objects.get(cbt_code=cbt_code)
        except Contractor_Business_Type.DoesNotExist:
            continue

        for syo_entry in syo_list:
            syo_code = syo_entry["syo_code"]
            perm_ids = syo_entry["permissions"]

            try:
                syo_instance = SystemObjects.objects.get(syo_code=syo_code)
            except SystemObjects.DoesNotExist:
                continue

            sop_instance, created = SystemObjectPermission.objects.get_or_create(
                cbt_code=cbt_instance,
                syo_code=syo_instance,
            )

            # Retrieve Permission instances
            permission_objs = PermissionStore.objects.filter(perm_id__in=perm_ids)
            missing_perms = set(perm_ids) - set(p.perm_id for p in permission_objs)

            # Add M2M relation
            sop_instance.sop_permissions.set(permission_objs)
            sop_instance.save()


def assign_syo_objects_to_company():
    # Add all permission to companies
    companies = company.objects.all()
    syo_codes = SystemObjects.objects.all()
    for cmp in companies:
        for syo in syo_codes:
            try:
                CompanyObjects.objects.get(cmp_id=cmp, syo_code=syo)
            except CompanyObjects.DoesNotExist:
                CompanyObjects.objects.create(cmp_id=cmp, syo_code=syo)


def create_company_role_permissions(system_role_permissions, company):
    for rol_type, syo_permissions in system_role_permissions.items():
        try:
            role = CompanyRole.objects.get(cmp_id=company, rol_type=rol_type)
        except CompanyRole.DoesNotExist:
            role = CompanyRole.objects.create(
                cmp_id=company,
                rol_type=rol_type,
                rol_name=f"{rol_type} Role",
            )

        for syo_code, permissions in syo_permissions.items():
            try:
                syo_obj = SystemObjects.objects.get(syo_code=syo_code)
            except SystemObjects.DoesNotExist:
                continue

            for perm_id in permissions:
                try:
                    permission = PermissionStore.objects.get(perm_id=perm_id)
                except PermissionStore.DoesNotExist:
                    continue
                crp_obj, created = CompanyRolePermission.objects.get_or_create(
                    cmp_id=company,
                    rol_id=role,
                    syo_code=syo_obj,
                    crp_permission=permission,
                )


def add_rbac():
    # Create permissions
    ps = create_permission_store(permissions)

    # Create system objects
    syo = create_syo_objects(system_objects)

    # Add permissions to system objects
    add_permissions_to_objects(permissions, objects_permissions)

    # Create system object permissions
    create_system_object_permissions(system_object_permission)

    # Assign system objects to companies
    assign_syo_objects_to_company()

    # Create company role permissions
    companies = company.objects.all()
    for cmp in companies:
        create_company_role_permissions(system_role_permissions, cmp)
