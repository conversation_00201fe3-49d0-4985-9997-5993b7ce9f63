from rest_framework.test import APITestCase, APIClient
from cost_data.models import ProjectCostSetup
from .data_factory import (
    create_company,
    create_user,
    create_employee,
    create_project,
    create_project_company,
    get_basic_cost_setup_data,
    create_company_role,
    create_company_employee_roles,
    add_rbac,
)


class ConfigTest(APITestCase):
    @classmethod
    def setUpTestData(cls):
        cls.company = create_company()
        cls.roles = create_company_role(cls.company)
        pm_role = cls.roles[0]
        fm_role = cls.roles[1]
        cls.login_user = create_user("testuser", "testpass")
        cls.employee = create_employee(cls.login_user, cls.company, pm_role)
        cls.employee_role = create_company_employee_roles(
            cls.employee, cls.company, pm_role
        )
        cls.project = create_project(cls.employee)
        cls.project_company = create_project_company(
            cls.project, cls.employee, cls.company
        )
        add_rbac()

    def setUp(self):
        self.client = APIClient()
        response = self.client.post(
            "/api-token-auth/",
            {"username": "testuser", "password": "testpass"},
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        self.token = response.data.get("access")
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {self.token}")

    def test_get_project_cost_setup(self):
        response = self.client.get(
            f"/api/v1/cost_data/recurring_cost/{self.project_company.pcm_id}/config/"
        )
        self.assertEqual(response.status_code, 200)

    def test_create_project_cost_setup(self):
        data = get_basic_cost_setup_data(
            self.project_company, self.project, self.employee
        )
        response = self.client.post(
            f"/api/v1/cost_data/recurring_cost/{self.project_company.pcm_id}/config/",
            data,
            format="json",
        )
        result = response.data.get("data", {})
        self.assertEqual(response.status_code, 201)
        self.assertEqual(result["pcm_id"], self.project_company.pcm_id)

    def test_create_project_cost_setup_invalid_choice(self):
        data = get_basic_cost_setup_data(
            self.project_company, self.project, self.employee
        )
        data["pcs_frequency"] = "WRONG_VALUE"
        response = self.client.post(
            f"/api/v1/cost_data/recurring_cost/{self.project_company.pcm_id}/config/",
            data,
            format="json",
        )
        self.assertEqual(response.status_code, 400)
        self.assertIn("pcs_frequency", response.data["message"])

    def test_retrieve_single_cost_config(self):
        payload = get_basic_cost_setup_data(
            self.project_company, self.project, self.employee
        )
        payload["pcm_id"] = self.project_company
        payload["pcs_approver"] = self.employee
        payload["prj_id"] = self.project
        payload["pcs_created_by"] = self.employee

        instance = ProjectCostSetup.objects.create(**payload)
        response = self.client.get(
            f"/api/v1/cost_data/recurring_cost/config/{instance.pcs_id}/"
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["data"]["pcs_id"], str(instance.pcs_id))

    def test_approve_cost_config(self):
        payload = get_basic_cost_setup_data(
            self.project_company, self.project, self.employee
        )
        payload["pcm_id"] = self.project_company
        payload["pcs_status"] = ProjectCostSetup.StatusChoices.OPEN
        payload["pcs_approver"] = self.employee
        payload["prj_id"] = self.project
        payload["pcs_created_by"] = self.employee

        instance = ProjectCostSetup.objects.create(**payload)
        data = {"pcs_ids": [str(instance.pcs_id)]}
        response = self.client.patch(
            "/api/v1/cost_data/recurring_cost/approve/", data, format="json"
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(
            response.data["data"][0]["pcs_status"],
            ProjectCostSetup.StatusChoices.ACCEPTED,
        )

    def test_delete_cost_config_invalid_status(self):
        payload = get_basic_cost_setup_data(
            self.project_company, self.project, self.employee
        )
        payload["pcm_id"] = self.project_company
        payload["pcs_status"] = ProjectCostSetup.StatusChoices.ACCEPTED
        payload["pcs_approver"] = self.employee
        payload["prj_id"] = self.project
        payload["pcs_created_by"] = self.employee

        instance = ProjectCostSetup.objects.create(**payload)
        response = self.client.delete(
            f"/api/v1/cost_data/recurring_cost/config/{instance.pcs_id}/"
        )
        self.assertEqual(response.status_code, 400)
        self.assertIn("cannot delete", response.data["message"].lower())

    def test_update_cost_config(self):
        payload = get_basic_cost_setup_data(
            self.project_company, self.project, self.employee
        )
        payload["pcm_id"] = self.project_company
        payload["pcs_status"] = ProjectCostSetup.StatusChoices.OPEN
        payload["pcs_approver"] = self.employee
        payload["prj_id"] = self.project
        payload["pcs_created_by"] = self.employee

        instance = ProjectCostSetup.objects.create(**payload)
        data = {"pcs_cost_name": "Updated Name", "pcs_rate": 1500.00}
        response = self.client.patch(
            f"/api/v1/cost_data/recurring_cost/config/{instance.pcs_id}/",
            data,
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["data"]["pcs_cost_name"], "Updated Name")

    def test_activation_deactivate_and_reactivate(self):
        payload = get_basic_cost_setup_data(
            self.project_company, self.project, self.employee
        )
        payload["pcm_id"] = self.project_company
        payload["pcs_status"] = ProjectCostSetup.StatusChoices.ACCEPTED
        payload["pcs_is_active"] = True
        payload["pcs_approver"] = self.employee
        payload["prj_id"] = self.project
        payload["pcs_created_by"] = self.employee

        instance = ProjectCostSetup.objects.create(**payload)
        # Deactivate
        data = {"action": "deactivate", "pcs_deactivation_reason": "Testing"}
        response = self.client.patch(
            f"/api/v1/cost_data/recurring_cost/{instance.pcs_id}/activation/",
            data,
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        self.assertFalse(response.data["data"]["pcs_is_active"])

        # Activate
        data = {"action": "activate"}
        response = self.client.patch(
            f"/api/v1/cost_data/recurring_cost/{instance.pcs_id}/activation/",
            data,
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.data["data"]["pcs_is_active"])

    def test_list_adhoc_costs(self):
        payload = get_basic_cost_setup_data(
            self.project_company, self.project, self.employee
        )
        payload["pcm_id"] = self.project_company
        payload["pcs_status"] = ProjectCostSetup.StatusChoices.ACCEPTED
        payload["pcs_is_active"] = True
        payload["pcs_approver"] = self.employee
        payload["prj_id"] = self.project
        payload["pcs_created_by"] = self.employee
        payload["pcs_frequency"] = ProjectCostSetup.FrequencyChoices.ADHOC
        payload["pcs_cost_code"] = "ADHOC-001"
        payload["pcs_cost_name"] = "Adhoc Cost Example"

        ProjectCostSetup.objects.create(**payload)
        response = self.client.get(
            f"/api/v1/cost_data/recurring_cost/{self.project_company.pcm_id}/adhoc/cost_code/"
        )
        self.assertEqual(response.status_code, 200)
        self.assertIsInstance(response.data["data"], list)

    def test_create_adhoc_cost(self):
        data = get_basic_cost_setup_data(
            self.project_company, self.project, self.employee
        )
        data["pcs_frequency"] = ProjectCostSetup.FrequencyChoices.ADHOC
        data["pcs_rate"] = None
        data["pcs_start_date"] = None
        data["pcs_end_date"] = None

        response = self.client.post(
            f"/api/v1/cost_data/recurring_cost/{self.project_company.pcm_id}/config/",
            data,
            format="json",
        )
        self.assertEqual(response.status_code, 201)
        self.assertEqual(
            response.data["data"]["pcs_frequency"],
            ProjectCostSetup.FrequencyChoices.ADHOC,
        )
