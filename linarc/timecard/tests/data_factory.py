# from random import choice
from faker import Faker
from projects.models import Project, Project_Company as ProjectCompany
from companies.models import company_employee
from companies.models import company_task_codes, company
from django.utils import timezone
from system.models import Building_Type, Building_Sub_Type, Contractor_Business_Type
from companies.models import login_user as LoginUser

fake = Faker()
import datetime


def create_company():
    return company.objects.create(
        cmp_name="Stark Industries",
        cmp_website="https://stark.com",
        cmp_status=company.status_choices.ACTIVE,
        cmp_is_onboarded=True,
        cmp_onboard_stage=company.OnboardingStage.COMPANY_INFO,
    )


def create_user(username, password):
    return LoginUser.objects.create_user(
        user_id=username, username=username, password=password
    )


def create_employee(user, company):
    return company_employee.objects.create(
        user_id=user.user_id,
        cmp_id=company,
        emp_first_name="Tony",
        emp_last_name="Stark",
        emp_email="<EMAIL>",
        emp_phone="1234567890",
        emp_status=company_employee.status_choices.ACTIVE,
    )


def create_project(employee):
    building_type = Building_Type.objects.create(blt_type="IN", blt_name="Industrial")
    # Create Building Sub Type
    building_sub_type = Building_Sub_Type.objects.create(
        bls_type="IN1", blt_type=building_type.blt_type, bls_name="Factory"
    )
    contractor_business_type = Contractor_Business_Type.objects.create(
        cbt_code="IN", cbt_name="Installer"
    )
    return Project.objects.create(
        prj_name="Iron Man Suit",
        prj_status="AC",
        prj_sched_type="GC",
        prj_schedule_mode="ST",
        prj_stage="DS",
        prj_start_date=datetime.date.today(),
        prj_end_date=datetime.date.today() + datetime.timedelta(days=30),
        prj_createdon=datetime.date.today(),
        prj_desc="Secret project by Stark",
        cnt_code="US",
        my_role_id="IN",
        project_type="IN",
        my_role=contractor_business_type,
        prj_createdby=employee,
        blt_type=building_type,
        bls_type=building_sub_type,
    )


def create_project_company(project, employee, company):
    return ProjectCompany.objects.create(
        prj_id=project,
        pcm_invitedby=employee,
        cmp_id=company,
        pcm_cbt_code="GC",
        pcm_cst_code="GC",
        pcm_status=ProjectCompany.status_choices.ACTIVE,
        pcm_start_date=timezone.now().date(),
        pcm_invitedon=timezone.now().date(),
    )
