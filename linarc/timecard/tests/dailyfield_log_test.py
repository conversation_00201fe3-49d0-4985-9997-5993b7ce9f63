from rest_framework.test import APITestCase, APIClient
from django.contrib.contenttypes.models import ContentType
from timecard.models import Daily<PERSON>ieldLog, DailyFieldLogSummary
from .data_factory import (
    create_company,
    create_user,
    create_employee,
    create_project,
    create_project_company,
)
import datetime
from django.utils import timezone


def get_basic_field_log_data(user_id, pcm_id):
    """Helper function to create basic field log data"""
    return {
        "dfl_date": datetime.date.today().isoformat(),
        "dfl_user_id": user_id,
        "dfl_created_pcm_id": pcm_id,
        "dfl_object_type": "project",
        "dfl_object_id": "test_object_id",
        "dfl_type": "note",
        "dfl_text": "Test field log entry",
        "dfl_data": {"weather": "sunny", "temperature": "72F"},
    }


class DailyFieldLogTest(APITestCase):
    @classmethod
    def setUpTestData(cls):
        cls.company = create_company()
        cls.login_user = create_user("testuser", "testpass")
        cls.employee = create_employee(cls.login_user, cls.company)
        cls.project = create_project(cls.employee)
        cls.project_company = create_project_company(
            cls.project, cls.employee, cls.company
        )

    def setUp(self):
        self.client = APIClient()
        response = self.client.post(
            "/api-token-auth/",
            {"username": "testuser", "password": "testpass"},
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        self.token = response.data.get("access")
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {self.token}")

    def test_create_daily_field_log(self):
        """Test creating a new daily field log"""
        data = get_basic_field_log_data(
            self.employee.user_id, self.project_company.pcm_id
        )
        response = self.client.post(
            f"/api/v1/timecard/dailyfieldlog/{self.project_company.pcm_id}/",
            data,
            format="json",
        )
        self.assertEqual(response.status_code, 201)
        self.assertIn("dfl_id", response.data)
        self.assertEqual(response.data["dfl_type"], "note")
        self.assertEqual(response.data["dfl_text"], "Test field log entry")

    def test_create_field_log_invalid_content_type(self):
        """Test creating field log with invalid content type"""
        data = get_basic_field_log_data(
            self.employee.user_id, self.project_company.pcm_id
        )
        data["dfl_object_type"] = "invalid_model"
        response = self.client.post(
            f"/api/v1/timecard/dailyfieldlog/{self.project_company.pcm_id}/",
            data,
            format="json",
        )
        self.assertEqual(response.status_code, 400)
        self.assertIn("Invalid content type", response.data["error"])

    def test_create_field_log_invalid_type_choice(self):
        """Test creating field log with invalid DFL type choice"""
        data = get_basic_field_log_data(
            self.employee.user_id, self.project_company.pcm_id
        )
        data["dfl_type"] = "INVALID_TYPE"
        response = self.client.post(
            f"/api/v1/timecard/dailyfieldlog/{self.project_company.pcm_id}/",
            data,
            format="json",
        )
        self.assertEqual(response.status_code, 400)

    def test_list_field_logs_by_date(self):
        """Test retrieving field logs by date"""
        # First create a log
        data = get_basic_field_log_data(
            self.employee.user_id, self.project_company.pcm_id
        )
        create_response = self.client.post(
            f"/api/v1/timecard/dailyfieldlog/{self.project_company.pcm_id}/",
            data,
            format="json",
        )
        self.assertEqual(create_response.status_code, 201)

        # Then retrieve logs for that date
        response = self.client.get(
            f"/api/v1/timecard/dailyfieldlog/{self.project_company.pcm_id}/",
            {"dfl_date": datetime.date.today().isoformat()},
        )
        self.assertEqual(response.status_code, 200)
        self.assertIn("data", response.data)
        self.assertIsInstance(response.data["data"], list)
        self.assertGreater(len(response.data["data"]), 0)

    def test_list_field_logs_missing_date(self):
        """Test retrieving field logs without providing date parameter"""
        response = self.client.get(
            f"/api/v1/timecard/dailyfieldlog/{self.project_company.pcm_id}/",
        )
        self.assertEqual(response.status_code, 400)
        self.assertIn("dfl_date query parameter is required", response.data["message"])

    def test_list_field_logs_no_logs_found(self):
        """Test retrieving field logs when no logs exist for the date"""
        future_date = (datetime.date.today() + datetime.timedelta(days=30)).isoformat()
        response = self.client.get(
            f"/api/v1/timecard/dailyfieldlog/{self.project_company.pcm_id}/",
            {"dfl_date": future_date},
        )
        self.assertEqual(response.status_code, 404)
        self.assertIn("No logs found", response.data["message"])

    def test_get_logs_by_pcm(self):
        """Test getting aggregated logs by PCM ID"""
        # Create some test logs
        for i in range(3):
            data = get_basic_field_log_data(
                self.employee.user_id, self.project_company.pcm_id
            )
            data["dfl_type"] = ["note", "safety", "weather"][i]
            self.client.post(
                f"/api/v1/timecard/dailyfieldlog/{self.project_company.pcm_id}/",
                data,
                format="json",
            )

        response = self.client.get(
            f"/api/v1/timecard/site-journal/{self.project_company.pcm_id}/",
        )
        self.assertEqual(response.status_code, 200)
        self.assertIsInstance(response.data, list)

        # Check that the response contains expected structure
        if response.data:
            log_entry = response.data[0]
            self.assertIn("dfl_date", log_entry)
            self.assertIn("dfs_id", log_entry)
            self.assertIn("site_suprintendent", log_entry)
            # Check that all DFL type choices are present with counts
            for choice, _ in DailyFieldLog.DFL_TYPE_CHOICES:
                self.assertIn(choice, log_entry)

    def test_close_day_logs(self):
        """Test closing all logs for a specific date"""
        # Create a test log
        data = get_basic_field_log_data(
            self.employee.user_id, self.project_company.pcm_id
        )
        create_response = self.client.post(
            f"/api/v1/timecard/dailyfieldlog/{self.project_company.pcm_id}/",
            data,
            format="json",
        )
        self.assertEqual(create_response.status_code, 201)

        # Close the day
        close_data = {"dfl_date": datetime.date.today().isoformat()}
        response = self.client.patch(
            f"/api/v1/timecard/dailyfieldlog/{self.project_company.pcm_id}/",
            close_data,
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        self.assertIn("logs marked as closed", response.data["message"])

    def test_close_day_missing_date(self):
        """Test closing day without providing date"""
        response = self.client.patch(
            f"/api/v1/timecard/dailyfieldlog/{self.project_company.pcm_id}/",
            {},
            format="json",
        )
        self.assertEqual(response.status_code, 400)
        self.assertIn("dfl_date is required", response.data["message"])

    def test_close_day_no_open_logs(self):
        """Test closing day when no open logs exist"""
        close_data = {"dfl_date": datetime.date.today().isoformat()}
        response = self.client.patch(
            f"/api/v1/timecard/dailyfieldlog/{self.project_company.pcm_id}/",
            close_data,
            format="json",
        )
        self.assertEqual(response.status_code, 404)
        self.assertIn("No open logs found", response.data["message"])
