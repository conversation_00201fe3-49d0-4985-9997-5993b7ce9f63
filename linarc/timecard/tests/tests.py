from rest_framework.test import APITestCase
import requests


class DailyJobSiteReport(APITestCase):

    # test case for correct data
    def test_get(self):
        headers = {
            "Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0b2tlbl90eXBlIjoiYWN"
            "jZXNzIiwiZXhwIjoxNjU2MDcxOTg3LCJqdGkiOiI1MGY4MzAwMWI2ZjU0YzY0YTU3ZTIxYjgwYzgxODk4NSIsIn"
            "VzZXJfaWQiOiI1IiwidXNlck5hbWUiOiJvbGl2aWFiMTIzOCJ9.DqIt-5y7CLNCqODY7hhM66iiygu_NMjpT7bkAUdf8Es"
        }
        url = "http://127.0.0.1:8000/api/v1/timecard/285/2022-04-05/RE/djsr/"
        response = requests.request("GET", url, headers=headers)
        self.assertEqual(response.status_code, 200)

    # test case for invalid date
    def test_date(self):
        headers = {
            "Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0b2tlbl90eXBlIjoiYWN"
            "jZXNzIiwiZXhwIjoxNjU2MDcxOTg3LCJqdGkiOiI1MGY4MzAwMWI2ZjU0YzY0YTU3ZTIxYjgwYzgxODk4NSIsIn"
            "VzZXJfaWQiOiI1IiwidXNlck5hbWUiOiJvbGl2aWFiMTIzOCJ9.DqIt-5y7CLNCqODY7hhM66iiygu_NMjpT7bkAUdf8Es"
        }
        url = "http://127.0.0.1:8000/api/v1/timecard/285/2022-04-07/S1/djsr/"
        response = requests.request("GET", url, headers=headers)
        self.assertEqual(response.status_code, 404)

    # test case for invalid pcm_id
    def test_code(self):
        headers = {
            "Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0b2tlbl90eXBlIjoiYWN"
            "jZXNzIiwiZXhwIjoxNjU2MDcxOTg3LCJqdGkiOiI1MGY4MzAwMWI2ZjU0YzY0YTU3ZTIxYjgwYzgxODk4NSIsIn"
            "VzZXJfaWQiOiI1IiwidXNlck5hbWUiOiJvbGl2aWFiMTIzOCJ9.DqIt-5y7CLNCqODY7hhM66iiygu_NMjpT7bkAUdf8Es"
        }
        url = "http://127.0.0.1:8000/api/v1/timecard/286/2022-04-07/S1/djsr/"
        response = requests.request("GET", url, headers=headers)

        self.assertEqual(response.status_code, 404)


class RefreshDailyJobSiteReport(APITestCase):
    # case 1 : current date
    def test_get(self):
        headers = {
            "Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0b2tlbl90eXBlIjoiYWN"
            "jZXNzIiwiZXhwIjoxNjU2MDcxOTg3LCJqdGkiOiI1MGY4MzAwMWI2ZjU0YzY0YTU3ZTIxYjgwYzgxODk4NSIsIn"
            "VzZXJfaWQiOiI1IiwidXNlck5hbWUiOiJvbGl2aWFiMTIzOCJ9.DqIt-5y7CLNCqODY7hhM66iiygu_NMjpT7bkAUdf8Es"
        }
        url = "http://127.0.0.1:8000/api/v1/timecard/285/2022-06-24/RE/createdjsr/"
        response = requests.request("POST", url, headers=headers)
        self.assertEqual(response.status_code, 200)

    # case 2 : random date
    def test_date(self):
        headers = {
            "Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0b2tlbl90eXBlIjoiYWN"
            "jZXNzIiwiZXhwIjoxNjU2MDcxOTg3LCJqdGkiOiI1MGY4MzAwMWI2ZjU0YzY0YTU3ZTIxYjgwYzgxODk4NSIsIn"
            "VzZXJfaWQiOiI1IiwidXNlck5hbWUiOiJvbGl2aWFiMTIzOCJ9.DqIt-5y7CLNCqODY7hhM66iiygu_NMjpT7bkAUdf8Es"
        }
        url = "http://127.0.0.1:8000/api/v1/timecard/285/2022-06-24/RE/createdjsr/"
        response = requests.request("POST", url, headers=headers)
        self.assertEqual(response.status_code, 200)

    # case 3 : different pcm data
    def test_pcmId(self):
        headers = {
            "Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0b2tlbl90eXBlIjoiYWN"
            "jZXNzIiwiZXhwIjoxNjU2MDcxOTg3LCJqdGkiOiI1MGY4MzAwMWI2ZjU0YzY0YTU3ZTIxYjgwYzgxODk4NSIsIn"
            "VzZXJfaWQiOiI1IiwidXNlck5hbWUiOiJvbGl2aWFiMTIzOCJ9.DqIt-5y7CLNCqODY7hhM66iiygu_NMjpT7bkAUdf8Es"
        }
        url = "http://127.0.0.1:8000/api/v1/timecard/286/2022-06-24/RE/createdjsr/"
        response = requests.request("POST", url, headers=headers)
        self.assertEqual(response.status_code, 200)

    # case 4 : wrong pcm data
    def test_pcm_id(self):
        headers = {
            "Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0b2tlbl90eXBlIjoiYWN"
            "jZXNzIiwiZXhwIjoxNjU2MDcxOTg3LCJqdGkiOiI1MGY4MzAwMWI2ZjU0YzY0YTU3ZTIxYjgwYzgxODk4NSIsIn"
            "VzZXJfaWQiOiI1IiwidXNlck5hbWUiOiJvbGl2aWFiMTIzOCJ9.DqIt-5y7CLNCqODY7hhM66iiygu_NMjpT7bkAUdf8Es"
        }
        url = "http://127.0.0.1:8000/api/v1/timecard/0/2022-06-24/RE/createdjsr/"
        response = requests.request("POST", url, headers=headers)
        self.assertEqual(response.status_code, 400)


class DailyFieldReport(APITestCase):
    def test_get(self):
        headers = {
            "Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0b2tlbl90eXBlIjoiYWN"
            "jZXNzIiwiZXhwIjoxNjU2MDcxOTg3LCJqdGkiOiI1MGY4MzAwMWI2ZjU0YzY0YTU3ZTIxYjgwYzgxODk4NSIsIn"
            "VzZXJfaWQiOiI1IiwidXNlck5hbWUiOiJvbGl2aWFiMTIzOCJ9.DqIt-5y7CLNCqODY7hhM66iiygu_NMjpT7bkAUdf8Es"
        }
        url = "http://127.0.0.1:8000/api/v1/timecard/160/2021-06-22/RE/dfr/"
        response = requests.request("GET", url, headers=headers)
        self.assertEqual(response.status_code, 200)
