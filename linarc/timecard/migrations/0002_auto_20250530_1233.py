# Generated by Django 3.2.6 on 2025-05-30 12:33

from django.db import migrations, models
import django.db.models.deletion
import shortuuid.django_fields


class Migration(migrations.Migration):

    dependencies = [
        ("companies", "0001_initial"),
        ("timecard", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="DailyFieldLogSummary",
            fields=[
                (
                    "dfs_id",
                    shortuuid.django_fields.ShortUUIDField(
                        alphabet=None,
                        length=16,
                        max_length=40,
                        prefix="",
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("created_on", models.DateField()),
                (
                    "user_id",
                    models.ForeignKey(
                        db_column="user_id",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="companies.company_employee",
                    ),
                ),
            ],
            options={
                "db_table": "daily_field_log_summary",
                "unique_together": {("user_id", "created_on")},
            },
        ),
        migrations.AddField(
            model_name="dailyfieldlog",
            name="dfs_id",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="timecard.dailyfieldlogsummary",
            ),
        ),
    ]
