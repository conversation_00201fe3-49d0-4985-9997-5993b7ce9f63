# Generated by Django 3.2.6 on 2025-06-09 12:52

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("external_drive", "0003_auto_20250531_0959"),
    ]

    operations = [
        migrations.AddField(
            model_name="driveprovisioning",
            name="project_folder_id",
            field=models.CharField(
                blank=True,
                help_text="folder id of the root project folder",
                max_length=100,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="gdriveauth",
            name="disable_switch",
            field=models.BooleanField(
                default=False, help_text="Indicates if Drive Switch is allowed"
            ),
        ),
        migrations.AddField(
            model_name="gdriveauth",
            name="drive_type",
            field=models.CharField(
                choices=[
                    ("SHARED_DRIVE", "Organization Shared Drive"),
                    ("MY_DRIVE", "User's My Drive"),
                ],
                default="SHARED_DRIVE",
                max_length=20,
            ),
        ),
        migrations.Add<PERSON>ield(
            model_name="gdriveauth",
            name="folder_id",
            field=models.<PERSON>r<PERSON><PERSON>(
                blank=True,
                help_text="Root Folder ID in the drive where files will be synced",
                max_length=100,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="driveprovisioning",
            name="auto_sync",
            field=models.BooleanField(
                db_index=True,
                default=False,
                help_text="Enable auto-sync for this drive",
            ),
        ),
    ]
