import datetime
from django.db.models.functions import TruncDate
from shared.serializers import AvailableEquipmentQuerySerializer
from shared.models.equipments_models import (
    EquipmentItem as Equipment,
    EquipmentAllocation,
    EquipmentSubCategory,
)


def find_available_equipment(payload):
    """
    Finds available equipment (Idle or returning before start date)
    AND excludes equipment with conflicting allocations during the requested period.
    """
    cmp_id = payload.get("cmp_id")
    if not cmp_id:
        return None

    payload = {
        "request_start_date": payload.get("request_start_date"),
        "request_end_date": payload.get("request_end_date"),
        "eqs_id": payload.get("eqs_id"),
    }

    input_serializer = AvailableEquipmentQuerySerializer(data=payload)
    if not input_serializer.is_valid():
        return None

    validated_data = input_serializer.validated_data
    request_start_date = validated_data["request_start_date"]  # datetime.date
    request_end_date = validated_data["request_end_date"]  # datetime.date
    sub_category_id = validated_data["eqs_id"]

    try:
        EquipmentSubCategory.objects.get(eqs_id=sub_category_id, cmp_id=cmp_id)
    except EquipmentSubCategory.DoesNotExist:
        return None

    try:
        # 1. Get IDLE equipment
        idle_equipment_qs = Equipment.objects.filter(
            cmp_id=cmp_id,
            eqp_status=Equipment.StatusTypes.IDLE,
            eqp_sub_category_id=sub_category_id,
        ).distinct()

        # 2. Get equipment returning BEFORE the request_start_date using TruncDate
        returning_allocations = (
            EquipmentAllocation.objects.annotate(
                alc_end_date_only=TruncDate("eqp_alc_end_date")
            )
            .filter(
                cmp_id=cmp_id,
                equipment__isnull=False,
                equipment__eqp_sub_category_id=sub_category_id,
                eqp_alc_end_date__isnull=False,
                alc_end_date_only__lt=request_start_date,  # Compare truncated date
                eqp_alc_type=EquipmentAllocation.AllocationType.INTERNAL,
                status__in=[
                    EquipmentAllocation.StatusChoices.ALLOCATED,
                    EquipmentAllocation.StatusChoices.DISPATCHED,
                    EquipmentAllocation.StatusChoices.DELIVERED,
                ],
            )
            .select_related(
                "equipment",
                "equipment__eqp_sub_category",
                "equipment__eqp_master_id",
            )
            .order_by("eqp_alc_end_date", "equipment_id")
        )

        # Process returning equipment
        returning_equipment_map = {}
        idle_equipment_ids = set(idle_equipment_qs.values_list("eqp_id", flat=True))
        for alloc in returning_allocations:
            if alloc.equipment_id in idle_equipment_ids:
                continue
            if alloc.equipment_id not in returning_equipment_map:
                returning_equipment_map[alloc.equipment_id] = {
                    "equipment": alloc.equipment,
                    "return_date": alloc.eqp_alc_end_date,
                }
        returning_equipment_sorted = [
            item["equipment"]
            for item in sorted(
                returning_equipment_map.values(), key=lambda x: x["return_date"]
            )
        ]
        returning_equipment_ids = {eq.eqp_id for eq in returning_equipment_sorted}

        # 3. Combine potential equipments IDs
        potential_equipment_ids = idle_equipment_ids.union(returning_equipment_ids)
        if not potential_equipment_ids:
            return []
            return Response([], status=status.HTTP_200_OK)  # No candidates

        # --- 4. Find Conflicting Allocations ---
        start_dt = datetime.datetime.combine(request_start_date, datetime.time.min)
        end_dt = datetime.datetime.combine(request_end_date, datetime.time.max)

        # Query for allocations overlapping the payload datetime range
        conflicting_allocations = EquipmentAllocation.objects.filter(
            equipment_id__in=potential_equipment_ids,
            cmp_id=cmp_id,
            eqp_alc_type=EquipmentAllocation.AllocationType.INTERNAL,
            eqp_alc_start_date__isnull=False,
            eqp_alc_end_date__isnull=False,
            # --- Standard Overlap condition: (StartA < EndB) and (EndA > StartB) ---
            eqp_alc_start_date__lt=end_dt,
            eqp_alc_end_date__gt=start_dt,
        )

        # Get IDs of equipment with conflicts
        conflicted_equipment_ids = set(
            conflicting_allocations.values_list("equipment_id", flat=True)
        )

        # --- 5. Filter the lists ---
        final_idle_list = [
            eq for eq in idle_equipment_qs if eq.eqp_id not in conflicted_equipment_ids
        ]
        final_returning_list_sorted = [
            eq
            for eq in returning_equipment_sorted
            if eq.eqp_id not in conflicted_equipment_ids
        ]

        # --- 6. Combine the final lists ---
        final_available_list = final_idle_list + final_returning_list_sorted

        return final_available_list

    except Exception as e:
        return None
